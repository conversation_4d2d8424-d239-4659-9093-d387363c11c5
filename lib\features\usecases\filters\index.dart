import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:tredo/core/localization/strings.dart';
import 'package:tredo/core/widgets/general/buttons/button.dart';
import 'package:tredo/features/usecases/states_cities/index.dart';

import '../category_subcategories/index.dart';
import 'controller.dart';
import 'models/filter_result.dart';
import 'models/filters_params.dart';

class FiltersBottomSheet extends StatelessWidget {
  final FiltersParams params;
  const FiltersBottomSheet({super.key, required this.params});

  static Future<FiltersResult?> showBottomSheet({
    required FiltersParams params,
  }) async => await Get.bottomSheet(FiltersBottomSheet(params: params));

  @override
  Widget build(BuildContext context) {
    // ignore: unused_local_variable
    FiltersController controller = Get.put(FiltersController(params));
    return BottomSheet(
      onClosing: () {
        Get.delete<FiltersController>();
      },
      builder: (context) {
        return Column(
          children: [
            const Gap(12),
            Row(
              children: [
                IconButton(
                  onPressed: () => Get.back(),
                  icon: const Icon(Icons.close_rounded),
                ),
                Expanded(
                  child: Text(
                    tr(LocaleKeys.filters),
                    textAlign: TextAlign.center,
                    style: context.textTheme.titleLarge,
                  ),
                ),
                TextButton(
                  onPressed: () => controller.resetFilters(),
                  child: Text(tr(LocaleKeys.reset)),
                ),
              ],
            ),
            if (params.activations.categories) const Gap(24),
            if (params.activations.categories)
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: CategoriesSubcategoriesWidget(
                  initialCategory: params.initialFilters.categoryId,
                  initialSubcategory: params.initialFilters.subcategoryId,
                  onCategoryChanged:
                      (value) => controller.result.categoryId = value?.id,
                  onSubcategoryChanged:
                      (value) => controller.result.subcategoryId = value?.id,
                  supportNone: true,
                  isRequired: false,
                ),
              ),

            if (params.activations.place) const Gap(24),
            if (params.activations.place)
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: StatesCitiesWidget(
                  initialState: params.initialFilters.stateId,
                  initialCity: params.initialFilters.cityId,
                  onStateChanged:
                      (value) => controller.result.stateId = value?.id,
                  onCityChanged:
                      (value) => controller.result.cityId = value?.id,
                  supportNone: true,
                  isRequired: false,
                ),
              ),
            const Gap(24),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: AppElevatedButton(
                onPressed: () => Get.back(result: controller.result),
                child: Text(tr(LocaleKeys.apply)),
              ),
            ),
          ],
        );
      },
    );
  }
}

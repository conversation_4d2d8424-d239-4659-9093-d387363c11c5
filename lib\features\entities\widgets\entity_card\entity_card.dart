import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:tredo/core/models/goverment_entity/goverment_entity.dart';
import 'package:tredo/core/widgets/general/loading/shimmer_loading.dart';
import 'package:tredo/core/widgets/general/svg_icon.dart';
import '../entity_details.dart';
import 'style/style.dart';

class EntityCard extends StatelessWidget {
  final GovermentEntity entity;
  const EntityCard({super.key, required this.entity});

  @override
  Widget build(BuildContext context) {
    final cardTheme = context.entityCardTheme;
    return InkWell(
      onTap: () => EntityDetailsBottomSheet.show(entity),
      child: Container(
        width: MediaQuery.sizeOf(context).width * .5,
        height: 50,
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: cardTheme.backgroundColor,
          borderRadius: BorderRadius.circular(8),
        ),
        child: IconTheme(
          data: IconTheme.of(context).copyWith(color: cardTheme.iconColor),
          child: Row(
            children: [
              SvgIconString(entity.svgImage, size: 24),
              const Gap(12),
              Expanded(
                child: Text(
                  "${entity.name} ",
                  style: cardTheme.style,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              const Gap(12),
              SvgIcon(Assets.icons.phoneCircle, size: 22),
            ],
          ),
        ),
      ),
    );
  }
}

class EntityCardLoading extends StatelessWidget {
  const EntityCardLoading({super.key});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: MediaQuery.sizeOf(context).width * .5,
      height: 50,
      child: ShimmerWidget(borderRadius: BorderRadius.circular(8)),
    );
  }
}

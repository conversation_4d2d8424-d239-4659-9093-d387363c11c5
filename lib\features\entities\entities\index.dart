import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:tredo/core/constants/controllers_tags.dart';
import 'package:tredo/core/models/goverment_entity/goverment_entity.dart';
import 'package:tredo/core/services/pagination/options/grid_view.dart';
import 'package:tredo/core/widgets/general/fields/search_field.dart';
import 'package:tredo/core/widgets/general/pages/scaffold.dart';

import 'controller.dart';
import 'widgets/app_bar.dart';
import '../widgets/entity_card/entity_card.dart';

class EntitiesPage extends StatelessWidget {
  const EntitiesPage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(EntitiesPageController());
    return AppScaffold(
      resizeToAvoidBottomInset: false,
      appBar: const EntitiesAppBar(),
      body: Column(
        children: [
          const Gap(16),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: SearchField(onChanged: controller.onSearch),
          ),
          const Gap(8),
          const Divider(height: 0),
          Expanded(
            child: GridViewPagination.aligned(
              tag: ControllersTags.entities_pager,
              fetchApi: controller.fetchEntities,
              fromJson: GovermentEntity.fromJson,
              onControllerInit:
                  (paginationController) =>
                      controller.pagerController = paginationController,
              padding: const EdgeInsets.only(
                top: 12,
                bottom: 70,
                left: 16,
                right: 16,
              ),
              crossAxisCount: 2,
              mainAxisSpacing: 16,
              crossAxisSpacing: 16,
              loading: const EntityCardLoading(),
              initialLoading: AlignedGridView.count(
                itemCount: 12,
                crossAxisCount: 2,
                mainAxisSpacing: 16,
                crossAxisSpacing: 16,
                padding: const EdgeInsets.only(
                  top: 12,
                  bottom: 70,
                  left: 16,
                  right: 16,
                ),
                itemBuilder: (context, index) => const EntityCardLoading(),
              ),
              itemBuilder:
                  (context, index, entity) => EntityCard(entity: entity),
            ),
          ),
        ],
      ),
    );
  }
}

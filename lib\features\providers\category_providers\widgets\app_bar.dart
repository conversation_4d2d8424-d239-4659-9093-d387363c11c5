import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tredo/core/widgets/general/buttons/back_button.dart';

import '../controller.dart';

class CategoryProvidersAppBar extends GetView<CategoryProvidersPageController> {
  const CategoryProvidersAppBar({super.key});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: MediaQuery.paddingOf(context).top + 100,
      child: NavigationToolbar(
        leading: const AppBackButton(),
        centerMiddle: true,
        middle: Text(
          controller.nav.category.name,
          style: context.textTheme.headlineSmall,
        ),
      ),
    );
  }
}

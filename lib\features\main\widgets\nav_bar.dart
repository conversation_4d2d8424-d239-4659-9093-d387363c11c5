// import 'package:curved_navigation_bar/curved_navigation_bar.dart';
import 'package:curved_labeled_navigation_bar/curved_navigation_bar.dart';
import 'package:curved_labeled_navigation_bar/curved_navigation_bar_item.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tredo/core/style/style.dart';
import 'package:tredo/core/widgets/general/svg_icon.dart';

import '../controller.dart';
import '../models/destination.dart';

class BottomNavBar extends GetView<MainPageController> {
  const BottomNavBar({super.key});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 80,
      child: Obx(
        () => DefaultTextStyle(
          style: context.textTheme.labelMedium!.copyWith(
            color: context.appColorScheme.onPrimaryColor,
          ),
          child: CurvedNavigationBar(
            animationDuration: 300.milliseconds,
            height: 80,
            backgroundColor: Colors.transparent,
            color: context.appColorScheme.primaryColor,
            buttonBackgroundColor: context.appColorScheme.secondaryColor,
            index: controller.pageIndex,
            items:
                HomeDestination.values
                    .map(
                      (e) => CurvedNavigationBarItem(
                        label: e == controller.destination ? e.text : "",
                        child: SvgIcon(
                          e == controller.destination
                              ? e.selectedIcon
                              : e.unselectedIcon,
                          color: context.appColorScheme.onPrimaryColor,
                          size: 24,
                        ),
                      ),
                    )
                    .toList(),
            onTap:
                (index) =>
                    controller.destination = HomeDestination.values[index],
          ),
        ),
      ),
    );
  }
}

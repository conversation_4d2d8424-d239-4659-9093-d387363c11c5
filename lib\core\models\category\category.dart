import 'package:easy_localization/easy_localization.dart';
import 'package:tredo/core/localization/strings.dart';

class Category {
  int id;
  String name;
  String svgImage;

  Category({required this.id, required this.name, required this.svgImage});

  static Category all = Category(
    id: 0,
    name: tr(LocaleKeys.all),
    svgImage:
        """<svg width="33" height="33" viewBox="0 0 33 33" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M26.5 0.5H6.5C3.19333 0.5 0.5 3.19333 0.5 6.5V26.5C0.5 29.8067 3.19333 32.5 6.5 32.5H26.5C29.8067 32.5 32.5 29.8067 32.5 26.5V6.5C32.5 3.19333 29.8067 0.5 26.5 0.5ZM31.1667 6.5V15.8333H17.1667V1.83333H26.5C29.0733 1.83333 31.1667 3.92667 31.1667 6.5ZM6.5 1.83333H15.8333V15.8333H1.83333V6.5C1.83333 3.92667 3.92667 1.83333 6.5 1.83333ZM1.83333 26.5V17.1667H15.8333V31.1667H6.5C3.92667 31.1667 1.83333 29.0733 1.83333 26.5ZM26.5 31.1667H17.1667V17.1667H31.1667V26.5C31.1667 29.0733 29.0733 31.1667 26.5 31.1667Z" fill="#CE74EA"/>
</svg>
""",
  );

  Category copyWith({int? id, String? name, String? svgImage}) => Category(
    id: id ?? this.id,
    name: name ?? this.name,
    svgImage: svgImage ?? this.svgImage,
  );

  factory Category.fromJson(Map<String, dynamic> json) =>
      Category(id: json["id"], name: json["name"], svgImage: json["svg_image"]);

  Map<String, dynamic> toJson() => {
    "id": id,
    "name": name,
    "svg_image": svgImage,
  };
}

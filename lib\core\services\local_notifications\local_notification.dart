import 'dart:io';

import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:get/get.dart';

class LocalNotificationService {
  static late FlutterLocalNotificationsPlugin notificationsService;

  static init({bool isBackGroundInit = false}) async {
    notificationsService = FlutterLocalNotificationsPlugin();

    //Andoid 13 permission
    if (GetPlatform.isAndroid && !isBackGroundInit) {
      await notificationsService
          .resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin
          >()!
          .requestNotificationsPermission();
    }

    var android = const AndroidInitializationSettings('@mipmap/ic_launcher');
    var ios = const DarwinInitializationSettings();
    var initSettings = InitializationSettings(android: android, iOS: ios);
    notificationsService.initialize(initSettings);
  }

  static showNotification({
    required int id,
    required String title,
    required String subTitle,
    required String payload,
  }) async {
    var android = AndroidNotificationDetails(
      'tredo',
      'tredo_channel',
      importance: Importance.max,
      priority: Priority.max,
      groupKey: 'group',
      setAsGroupSummary: true,
      enableVibration: true,
      groupAlertBehavior: GroupAlertBehavior.all,
      styleInformation: InboxStyleInformation(
        [subTitle],
        contentTitle: title,
        // summaryText: '',
      ),
    );
    var ios = const DarwinNotificationDetails();
    var platform = NotificationDetails(android: android, iOS: ios);
    await notificationsService.show(
      id,
      title,
      subTitle,
      platform,
      payload: payload,
    );
  }

  static Future<bool> areNotificationsEnabled() async {
    if (Platform.isAndroid) {
      // Check for Android
      final bool isNotificationEnabled =
          await notificationsService
              .resolvePlatformSpecificImplementation<
                AndroidFlutterLocalNotificationsPlugin
              >()
              ?.areNotificationsEnabled() ??
          false;

      return isNotificationEnabled;
    } else if (Platform.isIOS) {
      // Check for iOS
      final IOSFlutterLocalNotificationsPlugin? iosImplementation =
          notificationsService
              .resolvePlatformSpecificImplementation<
                IOSFlutterLocalNotificationsPlugin
              >();

      if (iosImplementation != null) {
        // On iOS, you need to request permission first before checking.
        final bool? isGranted = await iosImplementation.requestPermissions(
          alert: true,
          badge: true,
          sound: true,
        );

        return isGranted ?? false;
      }
    }

    // For platforms other than Android and iOS, return false (or you can add more checks).
    return false;
  }
}

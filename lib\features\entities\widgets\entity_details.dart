import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:tredo/core/models/goverment_entity/goverment_entity.dart';
import 'package:tredo/core/style/style.dart';
import 'package:tredo/core/widgets/general/data_sections/contact_details.dart';
import 'package:tredo/core/widgets/general/svg_icon.dart';

class EntityDetailsBottomSheet extends StatelessWidget {
  final GovermentEntity entity;
  const EntityDetailsBottomSheet({super.key, required this.entity});

  static Future show(GovermentEntity entity) async {
    return await Get.bottomSheet(EntityDetailsBottomSheet(entity: entity));
  }

  @override
  Widget build(BuildContext context) {
    return BottomSheet(
      onClosing: () {},
      builder:
          (context) => Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Gap(24),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                spacing: 12,
                children: [
                  SvgIconString(
                    entity.svgImage,
                    color: context.appColorScheme.primaryColor,
                    size: 25,
                  ),
                  Text(entity.name, style: context.textTheme.titleLarge),
                ],
              ),
              const Gap(24),
              ContactDetails(links: entity.socialLinks),
              const Gap(24),
            ],
          ),
    );
  }
}

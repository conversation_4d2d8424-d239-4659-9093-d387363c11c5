import 'package:flutter/material.dart';
import 'package:tredo/core/style/repo.dart';

enum ToastStatus {
  success,
  fail,
  warning;

  Color get color => switch (this) {
    success => StyleRepo.green,
    warning => StyleRepo.yellow,
    fail => StyleRepo.red,
  };

  IconData get icon => switch (this) {
    success => Icons.check_circle_outline,
    warning => Icons.warning_amber_rounded,
    fail => Icons.info_outline,
  };
}

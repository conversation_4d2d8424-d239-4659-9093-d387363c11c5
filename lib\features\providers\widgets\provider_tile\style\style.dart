// ignore_for_file: annotate_overrides

import 'package:flutter/material.dart';
import 'package:theme_tailor_annotation/theme_tailor_annotation.dart';
import 'package:tredo/core/style/repo.dart';

part 'style.tailor.dart';

@TailorMixin()
class ProviderTileTheme extends ThemeExtension<ProviderTileTheme>
    with _$ProviderTileThemeTailorMixin {
  ProviderTileTheme({
    required this.side,
    required this.imageShadow,
    required this.nameStyle,
    required this.viewsStyle,
  });

  final BorderSide side;
  final List<BoxShadow> imageShadow;
  final TextStyle nameStyle;
  final TextStyle viewsStyle;

  factory ProviderTileTheme.light(TextTheme textTheme) => ProviderTileTheme(
    side: const BorderSide(color: StyleRepo.darkGrey),
    imageShadow: [
      BoxShadow(
        color: StyleRepo.black.withValues(alpha: .25),
        blurRadius: 8,
        offset: const Offset(2, 1),
      ),
    ],
    nameStyle: textTheme.titleMedium!,
    viewsStyle: textTheme.bodyMedium!.copyWith(color: StyleRepo.darkGrey),
  );
}

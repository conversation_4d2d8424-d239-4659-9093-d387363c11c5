/// GENERATED CODE - DO NOT MODIFY BY HAND
/// *****************************************************
///  FlutterGen
/// *****************************************************

// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: directives_ordering,unnecessary_import,implicit_dynamic_list_literal,deprecated_member_use

import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_svg/flutter_svg.dart' as _svg;
import 'package:vector_graphics/vector_graphics.dart' as _vg;

class $AssetsFontsGen {
  const $AssetsFontsGen();

  /// File path: assets/fonts/Lato-Black.ttf
  String get latoBlack => 'assets/fonts/Lato-Black.ttf';

  /// File path: assets/fonts/Lato-Bold.ttf
  String get latoBold => 'assets/fonts/Lato-Bold.ttf';

  /// File path: assets/fonts/Lato-Light.ttf
  String get latoLight => 'assets/fonts/Lato-Light.ttf';

  /// File path: assets/fonts/Lato-Regular.ttf
  String get latoRegular => 'assets/fonts/Lato-Regular.ttf';

  /// File path: assets/fonts/Lato-Thin.ttf
  String get latoThin => 'assets/fonts/Lato-Thin.ttf';

  /// List of all assets
  List<String> get values => [
    latoBlack,
    latoBold,
    latoLight,
    latoRegular,
    latoThin,
  ];
}

class $AssetsIconsGen {
  const $AssetsIconsGen();

  /// File path: assets/icons/about_us.svg
  SvgGenImage get aboutUs => const SvgGenImage('assets/icons/about_us.svg');

  /// File path: assets/icons/arrow_back.svg
  SvgGenImage get arrowBack => const SvgGenImage('assets/icons/arrow_back.svg');

  /// File path: assets/icons/building.svg
  SvgGenImage get building => const SvgGenImage('assets/icons/building.svg');

  /// File path: assets/icons/camera.svg
  SvgGenImage get camera => const SvgGenImage('assets/icons/camera.svg');

  /// File path: assets/icons/categories_search.svg
  SvgGenImage get categoriesSearch =>
      const SvgGenImage('assets/icons/categories_search.svg');

  /// File path: assets/icons/check.svg
  SvgGenImage get check => const SvgGenImage('assets/icons/check.svg');

  /// File path: assets/icons/contact_us.svg
  SvgGenImage get contactUs => const SvgGenImage('assets/icons/contact_us.svg');

  /// File path: assets/icons/description.svg
  SvgGenImage get description =>
      const SvgGenImage('assets/icons/description.svg');

  /// File path: assets/icons/earth.svg
  SvgGenImage get earth => const SvgGenImage('assets/icons/earth.svg');

  /// File path: assets/icons/edit_box.svg
  SvgGenImage get editBox => const SvgGenImage('assets/icons/edit_box.svg');

  /// File path: assets/icons/entities_filled.svg
  SvgGenImage get entitiesFilled =>
      const SvgGenImage('assets/icons/entities_filled.svg');

  /// File path: assets/icons/entities_outlined.svg
  SvgGenImage get entitiesOutlined =>
      const SvgGenImage('assets/icons/entities_outlined.svg');

  /// File path: assets/icons/eye_closed.svg
  SvgGenImage get eyeClosed => const SvgGenImage('assets/icons/eye_closed.svg');

  /// File path: assets/icons/eye_opened.svg
  SvgGenImage get eyeOpened => const SvgGenImage('assets/icons/eye_opened.svg');

  /// File path: assets/icons/eye_outlined.svg
  SvgGenImage get eyeOutlined =>
      const SvgGenImage('assets/icons/eye_outlined.svg');

  /// File path: assets/icons/facebook.svg
  SvgGenImage get facebook => const SvgGenImage('assets/icons/facebook.svg');

  /// File path: assets/icons/filter.svg
  SvgGenImage get filter => const SvgGenImage('assets/icons/filter.svg');

  /// File path: assets/icons/home_filled.svg
  SvgGenImage get homeFilled =>
      const SvgGenImage('assets/icons/home_filled.svg');

  /// File path: assets/icons/home_outlined.svg
  SvgGenImage get homeOutlined =>
      const SvgGenImage('assets/icons/home_outlined.svg');

  /// File path: assets/icons/instagram.svg
  SvgGenImage get instagram => const SvgGenImage('assets/icons/instagram.svg');

  /// File path: assets/icons/languages.svg
  SvgGenImage get languages => const SvgGenImage('assets/icons/languages.svg');

  /// File path: assets/icons/lock.svg
  SvgGenImage get lock => const SvgGenImage('assets/icons/lock.svg');

  /// File path: assets/icons/logo.svg
  SvgGenImage get logo => const SvgGenImage('assets/icons/logo.svg');

  /// File path: assets/icons/no_data.svg
  SvgGenImage get noData => const SvgGenImage('assets/icons/no_data.svg');

  /// File path: assets/icons/notifications.svg
  SvgGenImage get notifications =>
      const SvgGenImage('assets/icons/notifications.svg');

  /// File path: assets/icons/offer_filled.svg
  SvgGenImage get offerFilled =>
      const SvgGenImage('assets/icons/offer_filled.svg');

  /// File path: assets/icons/offer_outlined.svg
  SvgGenImage get offerOutlined =>
      const SvgGenImage('assets/icons/offer_outlined.svg');

  /// File path: assets/icons/person.svg
  SvgGenImage get person => const SvgGenImage('assets/icons/person.svg');

  /// File path: assets/icons/phone.svg
  SvgGenImage get phone => const SvgGenImage('assets/icons/phone.svg');

  /// File path: assets/icons/phone_circle.svg
  SvgGenImage get phoneCircle =>
      const SvgGenImage('assets/icons/phone_circle.svg');

  /// File path: assets/icons/privacy_policy.svg
  SvgGenImage get privacyPolicy =>
      const SvgGenImage('assets/icons/privacy_policy.svg');

  /// File path: assets/icons/providers_filled.svg
  SvgGenImage get providersFilled =>
      const SvgGenImage('assets/icons/providers_filled.svg');

  /// File path: assets/icons/providers_outlined.svg
  SvgGenImage get providersOutlined =>
      const SvgGenImage('assets/icons/providers_outlined.svg');

  /// File path: assets/icons/search.svg
  SvgGenImage get search => const SvgGenImage('assets/icons/search.svg');

  /// File path: assets/icons/settings_filled.svg
  SvgGenImage get settingsFilled =>
      const SvgGenImage('assets/icons/settings_filled.svg');

  /// File path: assets/icons/settings_outlined.svg
  SvgGenImage get settingsOutlined =>
      const SvgGenImage('assets/icons/settings_outlined.svg');

  /// File path: assets/icons/shop.svg
  SvgGenImage get shop => const SvgGenImage('assets/icons/shop.svg');

  /// File path: assets/icons/tredo_text.svg
  SvgGenImage get tredoText => const SvgGenImage('assets/icons/tredo_text.svg');

  /// File path: assets/icons/whatsapp.svg
  SvgGenImage get whatsapp => const SvgGenImage('assets/icons/whatsapp.svg');

  /// List of all assets
  List<SvgGenImage> get values => [
    aboutUs,
    arrowBack,
    building,
    camera,
    categoriesSearch,
    check,
    contactUs,
    description,
    earth,
    editBox,
    entitiesFilled,
    entitiesOutlined,
    eyeClosed,
    eyeOpened,
    eyeOutlined,
    facebook,
    filter,
    homeFilled,
    homeOutlined,
    instagram,
    languages,
    lock,
    logo,
    noData,
    notifications,
    offerFilled,
    offerOutlined,
    person,
    phone,
    phoneCircle,
    privacyPolicy,
    providersFilled,
    providersOutlined,
    search,
    settingsFilled,
    settingsOutlined,
    shop,
    tredoText,
    whatsapp,
  ];
}

class $AssetsImagesGen {
  const $AssetsImagesGen();

  /// File path: assets/images/header_background.png
  AssetGenImage get headerBackground =>
      const AssetGenImage('assets/images/header_background.png');

  /// File path: assets/images/logo.png
  AssetGenImage get logo => const AssetGenImage('assets/images/logo.png');

  /// List of all assets
  List<AssetGenImage> get values => [headerBackground, logo];
}

class $AssetsTranslationsGen {
  const $AssetsTranslationsGen();

  /// File path: assets/translations/ar.json
  String get ar => 'assets/translations/ar.json';

  /// File path: assets/translations/en.json
  String get en => 'assets/translations/en.json';

  /// List of all assets
  List<String> get values => [ar, en];
}

class Assets {
  const Assets._();

  static const $AssetsFontsGen fonts = $AssetsFontsGen();
  static const $AssetsIconsGen icons = $AssetsIconsGen();
  static const $AssetsImagesGen images = $AssetsImagesGen();
  static const $AssetsTranslationsGen translations = $AssetsTranslationsGen();
}

class AssetGenImage {
  const AssetGenImage(this._assetName, {this.size, this.flavors = const {}});

  final String _assetName;

  final Size? size;
  final Set<String> flavors;

  Image image({
    Key? key,
    AssetBundle? bundle,
    ImageFrameBuilder? frameBuilder,
    ImageErrorWidgetBuilder? errorBuilder,
    String? semanticLabel,
    bool excludeFromSemantics = false,
    double? scale,
    double? width,
    double? height,
    Color? color,
    Animation<double>? opacity,
    BlendMode? colorBlendMode,
    BoxFit? fit,
    AlignmentGeometry alignment = Alignment.center,
    ImageRepeat repeat = ImageRepeat.noRepeat,
    Rect? centerSlice,
    bool matchTextDirection = false,
    bool gaplessPlayback = true,
    bool isAntiAlias = false,
    String? package,
    FilterQuality filterQuality = FilterQuality.medium,
    int? cacheWidth,
    int? cacheHeight,
  }) {
    return Image.asset(
      _assetName,
      key: key,
      bundle: bundle,
      frameBuilder: frameBuilder,
      errorBuilder: errorBuilder,
      semanticLabel: semanticLabel,
      excludeFromSemantics: excludeFromSemantics,
      scale: scale,
      width: width,
      height: height,
      color: color,
      opacity: opacity,
      colorBlendMode: colorBlendMode,
      fit: fit,
      alignment: alignment,
      repeat: repeat,
      centerSlice: centerSlice,
      matchTextDirection: matchTextDirection,
      gaplessPlayback: gaplessPlayback,
      isAntiAlias: isAntiAlias,
      package: package,
      filterQuality: filterQuality,
      cacheWidth: cacheWidth,
      cacheHeight: cacheHeight,
    );
  }

  ImageProvider provider({AssetBundle? bundle, String? package}) {
    return AssetImage(_assetName, bundle: bundle, package: package);
  }

  String get path => _assetName;

  String get keyName => _assetName;
}

class SvgGenImage {
  const SvgGenImage(this._assetName, {this.size, this.flavors = const {}})
    : _isVecFormat = false;

  const SvgGenImage.vec(this._assetName, {this.size, this.flavors = const {}})
    : _isVecFormat = true;

  final String _assetName;
  final Size? size;
  final Set<String> flavors;
  final bool _isVecFormat;

  _svg.SvgPicture svg({
    Key? key,
    bool matchTextDirection = false,
    AssetBundle? bundle,
    String? package,
    double? width,
    double? height,
    BoxFit fit = BoxFit.contain,
    AlignmentGeometry alignment = Alignment.center,
    bool allowDrawingOutsideViewBox = false,
    WidgetBuilder? placeholderBuilder,
    String? semanticsLabel,
    bool excludeFromSemantics = false,
    _svg.SvgTheme? theme,
    ColorFilter? colorFilter,
    Clip clipBehavior = Clip.hardEdge,
    @deprecated Color? color,
    @deprecated BlendMode colorBlendMode = BlendMode.srcIn,
    @deprecated bool cacheColorFilter = false,
  }) {
    final _svg.BytesLoader loader;
    if (_isVecFormat) {
      loader = _vg.AssetBytesLoader(
        _assetName,
        assetBundle: bundle,
        packageName: package,
      );
    } else {
      loader = _svg.SvgAssetLoader(
        _assetName,
        assetBundle: bundle,
        packageName: package,
        theme: theme,
      );
    }
    return _svg.SvgPicture(
      loader,
      key: key,
      matchTextDirection: matchTextDirection,
      width: width,
      height: height,
      fit: fit,
      alignment: alignment,
      allowDrawingOutsideViewBox: allowDrawingOutsideViewBox,
      placeholderBuilder: placeholderBuilder,
      semanticsLabel: semanticsLabel,
      excludeFromSemantics: excludeFromSemantics,
      colorFilter:
          colorFilter ??
          (color == null ? null : ColorFilter.mode(color, colorBlendMode)),
      clipBehavior: clipBehavior,
      cacheColorFilter: cacheColorFilter,
    );
  }

  String get path => _assetName;

  String get keyName => _assetName;
}

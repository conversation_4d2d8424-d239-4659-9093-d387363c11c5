import 'package:easy_localization/easy_localization.dart' hide TextDirection;
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:pinput/pinput.dart';
import 'package:tredo/core/localization/strings.dart';
import 'package:tredo/core/style/style.dart';
import 'package:tredo/core/widgets/general/buttons/button.dart';
import 'package:tredo/core/widgets/general/pages/scaffold.dart';

import '../widgets/appbar.dart';
import 'controller.dart';
import 'widgets/code_timer.dart';

class VerificationPage extends GetView<VerificationPageController> {
  const VerificationPage({super.key});

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      appBar: AuthAppBar(title: Text(tr(LocaleKeys.verify_your_phone_number))),
      body: ListView(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
        children: [
          Obx(
            () => Pinput(
              autofocus: true,
              defaultPinTheme: context.pinputTheme.defaultTheme,
              focusedPinTheme: context.pinputTheme.focused,
              submittedPinTheme: context.pinputTheme.submitted,
              errorPinTheme: context.pinputTheme.error,
              errorTextStyle: Theme.of(context).inputDecorationTheme.errorStyle,
              onChanged: (value) => controller.code = value,
              onTap: () => controller.error = '',
              errorText: controller.error,
              forceErrorState: controller.error.isNotEmpty,
            ),
          ),
          const Gap(32),
          const CodeTimer(),
          const Gap(32),
          Obx(
            () => AppElevatedButton(
              onPressed: controller.code.length == 4 ? controller.verify : null,
              child: Text(tr(LocaleKeys.verify)),
            ),
          ),
        ],
      ),
    );
  }
}

import 'package:get/get.dart';

import '../../config/app_builder.dart';
import '../../models/user/general_user.dart';
import '../rest_api/rest_api.dart';

class ProfileRefresher {
  static final ProfileRefresher instance = ProfileRefresher._();
  ProfileRefresher._();

  AppBuilder appBuilder = Get.find();

  refresh() async {
    ResponseModel response = await APIService.instance.requestAPI(
      Request(endPoint: EndPoints.my_profile, fromJson: GeneralUser.fromJson),
    );
    if (response.success) {
      appBuilder.setUser(response.data);
    }
  }
}

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:tredo/core/localization/strings.dart';
import 'package:tredo/core/style/repo.dart';
import 'package:tredo/core/style/style.dart';

class ErrorCard extends StatelessWidget {
  final String error;
  final void Function()? onRefresh;
  final BorderRadius? borderRadius;
  const ErrorCard({
    super.key,
    required this.error,
    this.onRefresh,
    this.borderRadius,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onRefresh,
        splashColor: context.appColorScheme.secondaryColor.shade100,
        highlightColor: context.appColorScheme.secondaryColor.shade100,
        borderRadius: borderRadius,
        child: Container(
          decoration: BoxDecoration(
            color: StyleRepo.grey.withValues(alpha: .3),
            borderRadius: borderRadius,
          ),
          child: Center(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              spacing: 12,
              children: [
                Text(
                  error,
                  style: TextStyle(color: context.appColorScheme.errorColor),
                ),
                if (onRefresh != null)
                  Text(
                    tr(LocaleKeys.click_to_refresh),
                    style: const TextStyle(color: StyleRepo.darkGrey),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

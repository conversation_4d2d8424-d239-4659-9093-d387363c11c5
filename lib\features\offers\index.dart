import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:tredo/core/constants/controllers_tags.dart';
import 'package:tredo/core/models/offer/offer.dart';
import 'package:tredo/core/services/pagination/options/grid_view.dart';
import 'package:tredo/core/widgets/general/fields/search_field.dart';
import 'package:tredo/core/widgets/general/loading/shimmer_loading.dart';
import 'package:tredo/core/widgets/general/pages/scaffold.dart';

import 'controller.dart';
import 'widgets/app_bar.dart';
import 'widgets/offer_card/offer_card.dart';

class OffersPage extends StatelessWidget {
  const OffersPage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(OffersPageController());
    return AppScaffold(
      resizeToAvoidBottomInset: false,
      appBar: const OffersAppBar(),
      body: Column(
        children: [
          const Gap(16),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: SearchField(onChanged: controller.onSearch),
          ),
          const Gap(8),
          const Divider(height: 0),
          Expanded(
            child: GridViewPagination.builder(
              tag: ControllersTags.offers_pager,
              fetchApi: controller.fetchOffers,
              fromJson: Offer.fromJson,
              onControllerInit:
                  (paginationController) =>
                      controller.pagerController = paginationController,
              padding: const EdgeInsets.only(
                top: 12,
                bottom: 70,
                right: 16,
                left: 16,
              ),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                mainAxisSpacing: 12,
                crossAxisSpacing: 12,
                childAspectRatio: OfferCard.aspectRatio,
              ),
              loading: ShimmerWidget(borderRadius: BorderRadius.circular(8)),
              initialLoading: GridView.builder(
                padding: const EdgeInsets.only(
                  top: 12,
                  bottom: 70,
                  right: 16,
                  left: 16,
                ),
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  mainAxisSpacing: 12,
                  crossAxisSpacing: 12,
                  childAspectRatio: OfferCard.aspectRatio,
                ),
                itemBuilder:
                    (_, __) =>
                        ShimmerWidget(borderRadius: BorderRadius.circular(8)),
              ),
              itemBuilder: (context, index, offer) => OfferCard(offer: offer),
            ),
          ),
        ],
      ),
    );
  }
}

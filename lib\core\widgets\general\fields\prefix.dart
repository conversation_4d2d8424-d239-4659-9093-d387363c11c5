import 'package:flutter/material.dart';
import 'package:tredo/core/style/style.dart';

class FieldPrefix extends StatelessWidget {
  final Widget child;
  final EdgeInsetsGeometry padding;

  const FieldPrefix({
    super.key,
    required this.child,
    this.padding = const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
  });

  @override
  Widget build(BuildContext context) {
    Widget prefixWidget = IconTheme(
      data: IconTheme.of(
        context,
      ).copyWith(color: context.appColorScheme.secondaryColor),
      child: DefaultTextStyle(
        style: TextStyle(color: context.appColorScheme.secondaryColor),
        child: Padding(padding: padding, child: child),
      ),
    );

    return prefixWidget;
  }
}

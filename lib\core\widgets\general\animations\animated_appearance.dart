import 'package:flutter/material.dart';

class AnimatedAppearance extends StatefulWidget {
  final Widget child;
  final Duration duration;
  final Widget Function(Widget child, Animation<double> animation)
  transitionBuilder;

  const AnimatedAppearance({
    super.key,
    this.transitionBuilder = defaultTransitionBuilder,
    required this.child,
    this.duration = const Duration(milliseconds: 300),
  });

  static Widget defaultTransitionBuilder(
    Widget child,
    Animation<double> animation,
  ) {
    return FadeTransition(
      key: ValueKey<Key?>(child.key),
      opacity: animation,
      child: child,
    );
  }

  @override
  createState() => _AnimatedAppearanceState();
}

class _AnimatedAppearanceState extends State<AnimatedAppearance> {
  bool displayed = false;

  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      setState(() {
        displayed = true;
      });
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedSwitcher(
      duration: widget.duration,
      transitionBuilder: widget.transitionBuilder,
      child: displayed ? widget.child : const SizedBox(),
    );
  }
}

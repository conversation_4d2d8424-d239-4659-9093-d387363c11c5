import 'package:get/get.dart';
import 'package:tredo/core/models/selection.dart';
import 'package:tredo/core/services/rest_api/rest_api.dart';
import 'package:tredo/core/services/state_management/obs.dart';

class StatesCitiesController extends GetxController {
  final int? initialState;
  final int? initialCity;

  StatesCitiesController({this.initialState, this.initialCity}) {
    _selectedStateId = (initialState ?? Selection.none.id).obs;
    selectedCityId = (initialCity ?? Selection.none.id);
  }

  @override
  onInit() {
    fetchStates();
    super.onInit();
  }

  //SECTION - States
  ObsList<Selection> states = ObsList([]);

  fetchStates() async {
    ResponseModel response = await APIService.instance.requestAPI(
      Request(endPoint: EndPoints.states, fromJson: Selection.fromJson),
    );
    if (response.success) {
      states.value = response.data;
      if (selectedStateId != Selection.none.id) {
        fetchCities();
      }
    } else {
      states.error = response.message;
    }
  }

  refreshStates() async {
    states.reset();
    selectedStateId = Selection.none.id;
    await fetchStates();
  }

  late final Rx<int> _selectedStateId;
  int get selectedStateId => _selectedStateId.value;
  set selectedStateId(int value) {
    _selectedStateId.value = value;
    if (value != Selection.none.id) {
      refreshCities();
    } else {
      selectedCityId = Selection.none.id;
      cities.reset();
    }
  }

  //!SECTION

  //SECTION - Cities
  ObsList<Selection> cities = ObsList([]);
  fetchCities() async {
    ResponseModel response = await APIService.instance.requestAPI(
      Request(
        endPoint: EndPoints.cities,
        params: {"state_ids[0]": selectedStateId},
        fromJson: Selection.fromJson,
      ),
    );
    if (response.success) {
      cities.value = response.data;
    } else {
      cities.error = response.message;
    }
  }

  refreshCities() async {
    cities.reset();
    selectedCityId = Selection.none.id;
    await fetchCities();
  }

  late int selectedCityId;

  //!SECTION
}

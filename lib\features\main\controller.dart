import 'package:get/get.dart';

import 'models/destination.dart';

class MainPageController extends GetxController {
  final Rx<HomeDestination> _destination = HomeDestination.home.obs;
  HomeDestination get destination => _destination.value;
  set destination(HomeDestination value) => _destination.value = value;

  int prevPageIndex = 2;
  int get pageIndex => HomeDestination.values.indexOf(destination);

  navToProviders() => destination = HomeDestination.providers;
}

import 'package:dio/dio.dart';
import 'package:get/get.dart';
import 'package:tredo/core/services/pagination/controller.dart';
import 'package:tredo/core/services/rest_api/rest_api.dart';

class EntitiesPageController extends GetxController {
  late PaginationController pagerController;

  Future<ResponseModel> fetchEntities(int page, CancelToken cancel) async {
    return await APIService.instance.requestAPI(
      Request(
        endPoint: EndPoints.entities,
        params: {"page": page, if (search.trim().isNotEmpty) "name": search},
        cancelToken: cancel,
      ),
    );
  }

  String search = "";
  onSearch(String search) {
    this.search = search;
    pagerController.refreshData();
  }
}

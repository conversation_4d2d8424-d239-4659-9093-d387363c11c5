import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tredo/core/config/app_builder.dart';
import 'package:tredo/core/config/role.dart';
import 'package:tredo/core/models/user/general_user.dart';
import 'package:tredo/core/services/firebase_messaging/firebase_messaging.dart';
import 'package:tredo/core/services/rest_api/rest_api.dart';
import 'package:tredo/core/widgets/general/toast/toast.dart';
import 'package:tredo/core/widgets/general/loading/loading.dart';

import '../../../core/routes/navigation.dart';
import '../verification/models/nav.dart';

class LoginPageController extends GetxController {
  AppBuilder appBuilder = Get.find();
  late TextEditingController phone, password;

  @override
  void onInit() {
    phone = TextEditingController();
    password = TextEditingController();
    super.onInit();
  }

  @override
  void onClose() {
    phone.dispose();
    password.dispose();
    super.onClose();
  }

  Future<void> login() async {
    Loading.show();

    ResponseModel response = await APIService.instance.requestAPI(
      Request(
        endPoint: EndPoints.login,
        method: RequestMethod.Post,
        body: {
          "phone": phone.text,
          "password": password.text,
          "fcm_token": await FirebaseMessagingService.getToken(),
        },
      ),
    );

    Loading.dispose();

    if (response.success) {
      if (!response.data['verified']) {
        bool result =
            (await Nav.to(
                  Pages.verification,
                  arguments: VerificationPageNav(phone: phone.text),
                ) ??
                false);
        if (!result) return;
      } else {
        appBuilder.setUserData(
          role: const User(),
          token: response.data['token'],
          user: GeneralUser.fromJson(response.data['user']),
        );
      }
      await appBuilder.role.initialize();
      Nav.offUntil(appBuilder.role.landing, (_) => false);
    } else {
      Toast.show(message: response.message);
    }
  }

  Future<void> loginAsGuest() async {
    appBuilder.setRole(const Guest());
    await appBuilder.role.initialize();
    Nav.to(appBuilder.role.landing);
  }

  void register() {
    Nav.to(Pages.register);
  }

  void forgetPassword() {
    Nav.to(Pages.forget_password);
  }
}

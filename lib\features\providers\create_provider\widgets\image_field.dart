import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tredo/core/style/style.dart';
import 'package:tredo/core/widgets/general/svg_icon.dart';
import 'package:tredo/core/widgets/image.dart';

import '../controller.dart';

class ProviderImageField extends GetView<CreateProviderPageController> {
  const ProviderImageField({super.key});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () => controller.pickImage(),
      child: Obx(
        () => AnimatedContainer(
          duration: 300.milliseconds,
          width: MediaQuery.sizeOf(context).width * .35,
          padding:
              controller.image.isNotEmpty
                  ? const EdgeInsets.all(1)
                  : const EdgeInsets.symmetric(horizontal: 32, vertical: 24),
          decoration: BoxDecoration(
            color: context.appColorScheme.secondaryColor.shade100,
            borderRadius: BorderRadius.circular(10),
          ),
          child: AnimatedSwitcher(
            duration: 300.milliseconds,
            child:
                controller.image.isEmpty
                    ? Center(
                      child: SvgIcon(
                        Assets.icons.camera,
                        color: context.appColorScheme.secondaryColor,
                      ),
                    )
                    : AspectRatio(
                      aspectRatio: 1,
                      child: AppImage(
                        path: controller.image,
                        type: ImageType.File,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10),
                        ),
                      ),
                    ),
          ),
        ),
      ),
    );
  }
}

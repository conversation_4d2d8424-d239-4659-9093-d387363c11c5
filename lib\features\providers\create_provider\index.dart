import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:tredo/core/localization/strings.dart';
import 'package:tredo/core/utils/validator.dart';
import 'package:tredo/core/widgets/general/buttons/button.dart';
import 'package:tredo/core/widgets/general/fields/phone.dart';
import 'package:tredo/core/widgets/general/fields/prefix.dart';
import 'package:tredo/core/widgets/general/pages/scaffold.dart';
import 'package:tredo/core/widgets/general/svg_icon.dart';
import 'package:tredo/features/auth/widgets/appbar.dart';
import 'package:tredo/features/usecases/category_subcategories/index.dart';
import 'package:tredo/features/usecases/states_cities/index.dart';

import 'controller.dart';
import 'widgets/image_field.dart';

class CreateProviderPage extends GetView<CreateProviderPageController> {
  const CreateProviderPage({super.key});

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      appBar: AuthAppBar(
        title: Text(tr(LocaleKeys.become_a_provider)),
        subtitle: Text(tr(LocaleKeys.fill_this_form_to_send_a_request)),
      ),
      body: Form(
        key: controller.formKey,
        child: ListView(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
          children: [
            const Center(child: ProviderImageField()),
            const Gap(16),
            Row(
              spacing: 12,
              children: [
                Expanded(
                  child: TextFormField(
                    controller: controller.providerName,
                    validator: Validator.notNull,
                    decoration: InputDecoration(
                      hintText: tr(LocaleKeys.provider_name),
                      prefixIcon: FieldPrefix(
                        child: SvgIcon(Assets.icons.person),
                      ),
                    ),
                  ),
                ),
                Expanded(
                  child: TextFormField(
                    controller: controller.shopName,
                    validator: Validator.notNull,
                    decoration: InputDecoration(
                      hintText: tr(LocaleKeys.shop_name),
                      prefixIcon: FieldPrefix(
                        child: SvgIcon(Assets.icons.shop),
                      ),
                    ),
                  ),
                ),
              ],
            ),
            const Gap(16),
            TextFormField(
              controller: controller.description,
              minLines: 3,
              maxLines: 3,
              validator: Validator.notNull,
              decoration: InputDecoration(
                hintText: tr(LocaleKeys.description),
                prefixIcon: FieldPrefix(
                  padding: const EdgeInsets.fromLTRB(16, 12, 16, 50),
                  child: SvgIcon(Assets.icons.description),
                ),
              ),
            ),
            const Gap(16),
            CategoriesSubcategoriesWidget(
              isRequired: true,
              onSubcategoryChanged:
                  (value) => controller.subcategoryId = value?.id,
            ),
            const Gap(16),
            PhoneField(controller: controller.phone),
            const Gap(16),
            StatesCitiesWidget(
              isRequired: true,
              onCityChanged: (value) => controller.cityId = value?.id,
            ),
            const Gap(16),
            TextFormField(
              controller: controller.whatsapp,
              decoration: InputDecoration(
                hintText: tr(LocaleKeys.enter_whatsapp_number),
                prefixIcon: IntrinsicHeight(
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      FieldPrefix(child: SvgIcon(Assets.icons.whatsapp)),
                      const VerticalDivider(width: 0),
                      const Gap(12),
                    ],
                  ),
                ),
              ),
            ),
            const Gap(16),
            TextFormField(
              controller: controller.instagram,
              decoration: InputDecoration(
                hintText: tr(LocaleKeys.enter_instagram_link),
                prefixIcon: IntrinsicHeight(
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      FieldPrefix(child: SvgIcon(Assets.icons.instagram)),
                      const VerticalDivider(width: 0),
                      const Gap(12),
                    ],
                  ),
                ),
              ),
            ),
            const Gap(16),
            TextFormField(
              controller: controller.facebook,
              decoration: InputDecoration(
                hintText: tr(LocaleKeys.enter_facebook_link),
                prefixIcon: IntrinsicHeight(
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      FieldPrefix(child: SvgIcon(Assets.icons.facebook)),
                      const VerticalDivider(width: 0),
                      const Gap(12),
                    ],
                  ),
                ),
              ),
            ),
            const Gap(16),

            AppElevatedButton(
              onPressed: controller.confirm,
              child: Text(tr(LocaleKeys.send_request)),
            ),
          ],
        ),
      ),
    );
  }
}

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:tredo/core/localization/strings.dart';
import 'package:tredo/core/services/state_management/widgets/obs_widget.dart';
import 'package:tredo/core/style/style.dart';
import 'package:tredo/core/widgets/general/error/error_card.dart';
import 'package:tredo/core/widgets/general/loading/shimmer_loading.dart';
import 'package:tredo/features/offers/widgets/offer_card/offer_card.dart';

import '../controller.dart';

class BestOffersList extends GetView<HomePageController> {
  const BestOffersList({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: RichText(
            text: TextSpan(
              style: context.textTheme.titleLarge,
              children: [
                TextSpan(text: "${tr(LocaleKeys.best)} "),
                TextSpan(
                  text: "${tr(LocaleKeys.offers)} ",
                  style: TextStyle(
                    color: context.appColorScheme.secondaryColor,
                  ),
                ),
                TextSpan(text: tr(LocaleKeys.just_for_you)),
              ],
            ),
          ),
        ),
        const Gap(16),
        ObsListBuilder(
          obs: controller.offers,
          loader:
              (context) => SizedBox(
                height: 200,
                child: ListView.separated(
                  scrollDirection: Axis.horizontal,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                  itemCount: 10,
                  separatorBuilder: (_, __) => const Gap(12),
                  itemBuilder:
                      (context, index) => AspectRatio(
                        aspectRatio: OfferCard.aspectRatio,
                        child: ShimmerWidget(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                ),
              ),
          errorBuilder:
              (context, error) => Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: SizedBox(
                  height: 200,
                  child: ErrorCard(
                    error: error,
                    onRefresh: controller.refreshOffers,
                    borderRadius: BorderRadius.circular(15),
                  ),
                ),
              ),
          builder: (context, offers) {
            return SizedBox(
              height: 200,
              child: ListView.separated(
                scrollDirection: Axis.horizontal,
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
                itemCount: offers.length,
                separatorBuilder: (_, __) => const Gap(12),
                itemBuilder:
                    (context, index) => OfferCard(
                      offer: offers[index],
                      withProviderData: false,
                    ),
              ),
            );
          },
        ),
      ],
    );
  }
}

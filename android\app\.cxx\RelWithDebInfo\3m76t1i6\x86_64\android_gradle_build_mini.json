{"buildFiles": ["C:\\src\\versions\\3.29.3\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\work\\ixCoders\\current\\tredo\\tredo-mobile-app\\android\\app\\.cxx\\RelWithDebInfo\\3m76t1i6\\x86_64", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\work\\ixCoders\\current\\tredo\\tredo-mobile-app\\android\\app\\.cxx\\RelWithDebInfo\\3m76t1i6\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:tredo/core/assets/assets.gen.dart';
import 'package:tredo/core/localization/strings.dart';
import 'package:tredo/core/services/state_management/widgets/obs_widget.dart';
import 'package:tredo/core/widgets/general/data_sections/contact_details.dart';
import 'package:tredo/core/widgets/general/error/error_card.dart';
import 'package:tredo/core/widgets/general/pages/scaffold.dart';
import 'package:tredo/core/widgets/version_widget.dart';

import 'controller.dart';

class AboutPage extends GetView<AboutPageController> {
  const AboutPage({super.key});

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      appBar: AppBar(title: Text(tr(LocaleKeys.about_us))),
      body: ListView(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
        children: [
          Assets.icons.tredoText.svg(
            width: MediaQuery.sizeOf(context).width * .5,
          ),
          const Gap(16),
          const VersionWidget(),
          const Gap(16),
          ObsVariableBuilder(
            obs: controller.about,
            builder: (context, about) {
              if (about.isEmpty) {
                return const SizedBox();
              }
              return Html(data: about);
            },
          ),
          const Gap(16),
          ObsListBuilder(
            obs: controller.links,
            errorBuilder:
                (context, error) => SizedBox(
                  height: 150,
                  child: ErrorCard(
                    error: error,
                    onRefresh: controller.refreshLinks,
                    borderRadius: BorderRadius.circular(15),
                  ),
                ),
            builder: (context, links) => ContactDetails(links: links),
          ),
        ],
      ),
    );
  }
}

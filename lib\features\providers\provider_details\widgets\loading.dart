import 'package:flutter/widgets.dart';
import 'package:gap/gap.dart';
import 'package:tredo/core/widgets/general/buttons/back_button.dart';
import 'package:tredo/core/widgets/general/loading/shimmer_loading.dart';

class ProviderDetailsPageLoading extends StatelessWidget {
  const ProviderDetailsPageLoading({super.key});

  @override
  Widget build(BuildContext context) {
    return ListView(
      children: [
        const Align(
          alignment: AlignmentDirectional.topStart,
          child: Padding(
            padding: EdgeInsets.all(8.0),
            child: AppBackButton(withBorder: true, withShadow: false),
          ),
        ),
        const SizedBox(height: 200, child: ShimmerWidget()),
        const Gap(24),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(
                height: 50,
                width: MediaQuery.sizeOf(context).width * .4,
                child: ShimmerWidget(borderRadius: BorderRadius.circular(8)),
              ),
              const Gap(24),
              SizedBox(
                height: 30,
                width: MediaQuery.sizeOf(context).width * .6,
                child: ShimmerWidget(borderRadius: BorderRadius.circular(8)),
              ),
            ],
          ),
        ),
      ],
    );
  }
}

class ImageModel {
  int id;
  String name;
  String collectionName;
  String mimeType;
  String original;
  String small;
  String medium;
  String large;

  bool get isVideo => mimeType.contains("video");
  bool get isImage => mimeType.contains("image");

  ImageModel({
    required this.id,
    required this.name,
    required this.collectionName,
    required this.mimeType,
    required this.original,
    required this.small,
    required this.medium,
    required this.large,
  });

  factory ImageModel.fromJson(dynamic json) {
    if (json == null ||
        (json is String && json.isEmpty) ||
        (json is! String && json is! Map<String, dynamic>)) {
      return ImageModel.empty();
    }
    if (json is String) {
      return ImageModel(
        id: 0,
        name: "",
        collectionName: "",
        mimeType: "",
        original: json,
        small: json,
        medium: json,
        large: json,
      );
    }
    return ImageModel(
      id: json["id"] ?? 0,
      name: json["name"] ?? "",
      collectionName: json["collection_name"] ?? "",
      mimeType: json["mime_type"] ?? "",
      original: json["original_url"] ?? "",
      small: json["preview_150"] ?? json["original_url"] ?? "",
      medium: json["preview_300"] ?? json["original_url"] ?? "",
      large: json["preview_600"] ?? json["original_url"] ?? "",
    );
  }

  factory ImageModel.tryParse(Map<String, dynamic>? json) {
    try {
      return ImageModel.fromJson(json!);
    } catch (_) {
      return ImageModel.empty();
    }
  }

  factory ImageModel.empty() => ImageModel(
    id: 0,
    name: "",
    collectionName: "",
    mimeType: "",
    original: "",
    small: "",
    medium: "",
    large: "",
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "name": name,
    "collection_name": collectionName,
    "mime_type": mimeType,
    "original_url": original,
    "preview_150": small,
    "preview_300": medium,
    "preview_600": large,
  };
}

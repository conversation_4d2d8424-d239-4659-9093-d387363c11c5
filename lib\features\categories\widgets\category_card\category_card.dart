import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:tredo/core/models/category/category.dart';
import 'package:tredo/core/style/style.dart';
import 'package:tredo/core/widgets/general/loading/shimmer_loading.dart';
import 'package:tredo/core/widgets/general/svg_icon.dart';
import 'package:tredo/features/categories/widgets/category_card/style/style.dart';

class CategoryCard extends StatelessWidget {
  final Category category;
  final bool selected;
  final void Function()? onTap;

  const CategoryCard({
    super.key,
    required this.category,
    this.selected = false,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final cardTheme = context.categoryCardTheme;
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(5),
        child: Column(
          children: [
            Expanded(
              child: LayoutBuilder(
                builder: (context, constraints) {
                  return Container(
                    height: constraints.maxHeight,
                    width: constraints.maxHeight,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: cardTheme.backgroundColor,
                    ),
                    child: Center(
                      child: SvgIconString(
                        category.svgImage,
                        color:
                            selected
                                ? context.appColorScheme.secondaryColor
                                : cardTheme.foregroundColor,
                        size: constraints.maxHeight * .5,
                      ),
                    ),
                  );
                },
              ),
            ),
            const Gap(8),
            SizedBox(
              width: MediaQuery.sizeOf(context).width * .2,
              child: Text(
                category.name,
                style: cardTheme.style.copyWith(
                  color:
                      selected ? context.appColorScheme.secondaryColor : null,
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class CategoryCardLoading extends StatelessWidget {
  const CategoryCardLoading({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        SizedBox(
          height: 60,
          width: 60,
          child: ShimmerWidget(borderRadius: BorderRadius.circular(30)),
        ),
        const Gap(8),
        SizedBox(
          width: MediaQuery.sizeOf(context).width * .2,
          child: SizedBox(
            height: 15,
            child: ShimmerWidget(borderRadius: BorderRadius.circular(5)),
          ),
        ),
      ],
    );
  }
}

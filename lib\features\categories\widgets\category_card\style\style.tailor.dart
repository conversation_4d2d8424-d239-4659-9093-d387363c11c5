// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'style.dart';

// **************************************************************************
// TailorAnnotationsGenerator
// **************************************************************************

mixin _$CategoryCardThemeTailorMixin on ThemeExtension<CategoryCardTheme> {
  Color get backgroundColor;
  Color get foregroundColor;
  TextStyle get style;

  @override
  CategoryCardTheme copyWith({
    Color? backgroundColor,
    Color? foregroundColor,
    TextStyle? style,
  }) {
    return CategoryCardTheme(
      backgroundColor: backgroundColor ?? this.backgroundColor,
      foregroundColor: foregroundColor ?? this.foregroundColor,
      style: style ?? this.style,
    );
  }

  @override
  CategoryCardTheme lerp(
    covariant ThemeExtension<CategoryCardTheme>? other,
    double t,
  ) {
    if (other is! CategoryCardTheme) return this as CategoryCardTheme;
    return CategoryCardTheme(
      backgroundColor: Color.lerp(backgroundColor, other.backgroundColor, t)!,
      foregroundColor: Color.lerp(foregroundColor, other.foregroundColor, t)!,
      style: TextStyle.lerp(style, other.style, t)!,
    );
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is CategoryCardTheme &&
            const DeepCollectionEquality().equals(
              backgroundColor,
              other.backgroundColor,
            ) &&
            const DeepCollectionEquality().equals(
              foregroundColor,
              other.foregroundColor,
            ) &&
            const DeepCollectionEquality().equals(style, other.style));
  }

  @override
  int get hashCode {
    return Object.hash(
      runtimeType.hashCode,
      const DeepCollectionEquality().hash(backgroundColor),
      const DeepCollectionEquality().hash(foregroundColor),
      const DeepCollectionEquality().hash(style),
    );
  }
}

extension CategoryCardThemeBuildContextProps on BuildContext {
  CategoryCardTheme get categoryCardTheme =>
      Theme.of(this).extension<CategoryCardTheme>()!;
  Color get backgroundColor => categoryCardTheme.backgroundColor;
  Color get foregroundColor => categoryCardTheme.foregroundColor;
  TextStyle get style => categoryCardTheme.style;
}

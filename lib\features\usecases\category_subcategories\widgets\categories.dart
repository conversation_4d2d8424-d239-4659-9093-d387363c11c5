import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:tredo/core/assets/assets.gen.dart';
import 'package:tredo/core/localization/strings.dart';
import 'package:tredo/core/models/selection.dart';
import 'package:tredo/core/services/state_management/widgets/obs_widget.dart';
import 'package:tredo/core/utils/validator.dart';
import 'package:tredo/core/widgets/general/fields/dropdown_filed.dart';
import 'package:tredo/core/widgets/general/fields/field_error.dart';
import 'package:tredo/core/widgets/general/fields/field_loading.dart';
import 'package:tredo/core/widgets/general/fields/required_filed.dart';

import '../controller.dart';

class CategoriesDropdown extends GetView<CategoriesSubcategoriesController> {
  final int? initialCategory;
  final void Function(Selection? value)? onCategoryChanged;
  final bool supportNone;
  final bool isRequired;
  const CategoriesDropdown(
    this.initialCategory,
    this.onCategoryChanged,
    this.supportNone,
    this.isRequired, {
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return ObsListBuilder(
      obs: controller.categories,
      loader:
          (context) =>
              isRequired
                  ? const RequiredField(child: FieldLoadingWidget())
                  : const FieldLoadingWidget(),
      errorBuilder:
          (context, error) =>
              isRequired
                  ? RequiredField(
                    child: FieldErrorWidget(
                      error: error,
                      onRefresh: controller.refreshCategories,
                    ),
                  )
                  : FieldErrorWidget(
                    error: error,
                    onRefresh: controller.refreshCategories,
                  ),
      builder: (context, categories) {
        return AppDropdownField(
          hint: tr(LocaleKeys.category),
          value: categories.firstWhereOrNull(
            (element) => element.id == initialCategory,
          ),
          validator: isRequired ? Validator.notNull : null,
          icon: Assets.icons.categoriesSearch,
          selections: categories,
          supportNone: supportNone,
          onChanged: (value) {
            onCategoryChanged?.call(value);
            controller.selectedCategoryId = value?.id ?? Selection.none.id;
          },
        );
      },
    );
  }
}

import 'package:get/get.dart';
import 'package:tredo/core/widgets/general/guest_bottom_sheet.dart';

import 'app_builder.dart';
import 'role.dart';

/// Helper class to check roles
abstract class RoleMiddleware {
  /// Used to check if current user role is Guest
  static bool get checkIfGuest => Get.find<AppBuilder>().role is Guest;

  /// Used to check if current user role is Guest then open a dialog of guest user
  static bool get guestForbidden {
    if (checkIfGuest) {
      Get.bottomSheet(const GuestBottomSheet());
      return true;
    } else {
      return false;
    }
  }
}

class BlockGuest extends GetMiddleware {
  @override
  GetPage? onPageCalled(GetPage? page) =>
      RoleMiddleware.guestForbidden ? null : page;
}

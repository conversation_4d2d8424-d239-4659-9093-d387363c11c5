import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tredo/core/constants/controllers_tags.dart';
import 'package:tredo/core/localization/strings.dart';
import 'package:tredo/core/services/pagination/options/list_view.dart';
import 'package:tredo/core/widgets/general/pages/scaffold.dart';
import 'package:tredo/features/notifications/widgets/card.dart';

import 'controller.dart';
import 'models/notification.dart';

class NotificationsPage extends GetView<NotificationsPageController> {
  const NotificationsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      appBar: AppBar(title: Text(tr(LocaleKeys.notifications))),
      body: ListViewPagination.separated(
        tag: ControllersTags.notificatoins_pager,
        fetchApi: controller.fetchNotifications,
        fromJson: NotificationModel.fromJson,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
        separatorBuilder: (_, __) => const Divider(),
        itemBuilder:
            (context, index, notification) =>
                NotificationCard(notification: notification),
      ),
    );
  }
}

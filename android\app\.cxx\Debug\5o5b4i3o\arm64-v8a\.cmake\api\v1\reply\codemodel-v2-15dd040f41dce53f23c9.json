{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.6.0"}, "projectIndex": 0, "source": "."}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "Project"}], "targets": []}], "kind": "codemodel", "paths": {"build": "D:/work/ixCoders/current/tredo/tredo-mobile-app/android/app/.cxx/Debug/5o5b4i3o/arm64-v8a", "source": "C:/src/versions/3.29.3/packages/flutter_tools/gradle/src/main/groovy"}, "version": {"major": 2, "minor": 3}}
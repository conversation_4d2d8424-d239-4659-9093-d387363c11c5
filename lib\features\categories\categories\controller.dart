import 'package:dio/dio.dart';
import 'package:get/get.dart';
import 'package:tredo/core/services/rest_api/rest_api.dart';

class CategoriesPageController extends GetxController {
  Future<ResponseModel> fetchCategories(int page, CancelToken cancel) async {
    return await APIService.instance.requestAPI(
      Request(
        endPoint: EndPoints.categories,
        params: {"page": page, "pager": null},
        cancelToken: cancel,
      ),
    );
  }
}

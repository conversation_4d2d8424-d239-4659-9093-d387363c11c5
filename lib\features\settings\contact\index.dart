import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:tredo/core/localization/strings.dart';
import 'package:tredo/core/utils/validator.dart';
import 'package:tredo/core/widgets/general/buttons/button.dart';
import 'package:tredo/core/widgets/general/fields/phone.dart';
import 'package:tredo/core/widgets/general/fields/prefix.dart';
import 'package:tredo/core/widgets/general/pages/scaffold.dart';
import 'package:tredo/core/widgets/general/svg_icon.dart';

import 'controller.dart';

class ContactPage extends GetView<ContactPageController> {
  const ContactPage({super.key});

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      appBar: AppBar(title: Text(tr(LocaleKeys.contact_us))),
      body: Form(
        key: controller.formKey,
        child: ListView(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
          children: [
            TextFormField(
              controller: controller.firstName,
              validator: Validator.notNull,
              decoration: InputDecoration(
                hintText: tr(LocaleKeys.first_name),
                prefixIcon: FieldPrefix(child: SvgIcon(Assets.icons.person)),
              ),
            ),
            const Gap(16),
            TextFormField(
              controller: controller.lastName,
              validator: Validator.notNull,
              decoration: InputDecoration(
                hintText: tr(LocaleKeys.last_name),
                prefixIcon: FieldPrefix(child: SvgIcon(Assets.icons.person)),
              ),
            ),
            const Gap(16),
            PhoneField(controller: controller.phone),
            const Gap(16),
            TextFormField(
              controller: controller.description,
              minLines: 3,
              maxLines: 3,
              validator: Validator.notNull,
              decoration: InputDecoration(
                hintText: tr(LocaleKeys.description),
                prefixIcon: FieldPrefix(
                  padding: const EdgeInsets.fromLTRB(16, 12, 16, 50),
                  child: SvgIcon(Assets.icons.description),
                ),
              ),
            ),
            const Gap(16),
            AppElevatedButton(
              onPressed: controller.confirm,
              child: Text(tr(LocaleKeys.send)),
            ),
          ],
        ),
      ),
    );
  }
}

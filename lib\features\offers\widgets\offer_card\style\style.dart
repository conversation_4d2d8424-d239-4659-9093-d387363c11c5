// ignore_for_file: annotate_overrides

import 'package:flutter/material.dart';
import 'package:theme_tailor_annotation/theme_tailor_annotation.dart';
import 'package:tredo/core/style/repo.dart';
import 'package:tredo/core/style/style.dart';

part 'style.tailor.dart';

@TailorMixin()
class OfferCardTheme extends ThemeExtension<OfferCardTheme>
    with _$OfferCardThemeTailorMixin {
  OfferCardTheme({
    required this.providerAvatarBorderColor,
    required this.shadow,
    required this.providerDataBackgroundColor,
    required this.providerNameStyle,
  });

  final Color providerAvatarBorderColor;
  final List<BoxShadow> shadow;
  final Color providerDataBackgroundColor;
  final TextStyle providerNameStyle;

  factory OfferCardTheme.light(
    AppColorScheme colorScheme,
    TextTheme textTheme,
  ) => OfferCardTheme(
    providerAvatarBorderColor: colorScheme.onPrimaryColor,
    shadow: [
      BoxShadow(
        color: colorScheme.primaryColor.shade200,
        blurRadius: 10,
        spreadRadius: 1,
      ),
    ],
    providerDataBackgroundColor: StyleRepo.black.withValues(alpha: .5),
    providerNameStyle: textTheme.bodyMedium!.copyWith(
      color: colorScheme.onPrimaryColor,
      fontWeight: FontWeight.w500,
    ),
  );
}

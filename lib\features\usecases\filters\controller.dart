import 'package:get/get.dart';
import 'package:tredo/features/usecases/filters/models/filter_result.dart';

import 'models/filters_params.dart';

class FiltersController extends GetxController {
  final FiltersParams params;
  FiltersController(this.params) {
    result = params.initialFilters.toFiltersResult();
  }

  late FiltersResult result;

  resetFilters() {
    result = FiltersResult();
    Get.back(result: result);
  }
}

import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:tredo/core/models/selection.dart';

import 'controller.dart';
import 'widgets/categories.dart';
import 'widgets/subcategories.dart';

class CategoriesSubcategoriesWidget extends StatelessWidget {
  final int? initialCategory;
  final int? initialSubcategory;
  final void Function(Selection? value)? onCategoryChanged;
  final void Function(Selection? value)? onSubcategoryChanged;
  final bool supportNone;
  final bool isRequired;

  const CategoriesSubcategoriesWidget({
    super.key,
    this.initialCategory,
    this.initialSubcategory,
    this.onCategoryChanged,
    this.onSubcategoryChanged,
    this.supportNone = false,
    this.isRequired = false,
  });

  @override
  Widget build(BuildContext context) {
    Get.put(
      CategoriesSubcategoriesController(
        initialSubcategory: initialSubcategory,
        initialCategory: initialCategory,
      ),
    );

    return Row(
      spacing: 12,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: CategoriesDropdown(
            initialCategory,
            onCategoryChanged,
            supportNone,
            isRequired,
          ),
        ),
        Expanded(
          child: SubcategoriesDropdown(
            initialSubcategory,
            onSubcategoryChanged,
            supportNone,
            isRequired,
          ),
        ),
      ],
    );
  }
}

import 'package:easy_localization/easy_localization.dart';
import 'package:tredo/core/localization/strings.dart';

class APIErrorMessages {
  static String noInternet = tr(LocaleKeys.no_internt);
  static String noData = tr(LocaleKeys.no_data);
  static String noAuth = tr(LocaleKeys.no_auth);
  static String forbidden = tr(LocaleKeys.forbidden);
  static String validation = tr(LocaleKeys.validation_error);
  static String server = tr(LocaleKeys.server_error);
  static String unknown = tr(LocaleKeys.unknown_error);
  static String canceled = tr(LocaleKeys.canceled);
}

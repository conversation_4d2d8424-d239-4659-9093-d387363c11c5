import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:tredo/core/localization/strings.dart';
import 'package:tredo/core/models/provider/main_provider.dart';
import 'package:tredo/core/routes/navigation.dart';
import 'package:tredo/core/style/style.dart';
import 'package:tredo/core/widgets/general/svg_icon.dart';
import 'package:tredo/core/widgets/general/tags_row.dart';
import 'package:tredo/core/widgets/image.dart';
import 'package:tredo/features/providers/provider_details/models/nav.dart';
import 'style/style.dart';

class ProviderCard extends StatelessWidget {
  final MainProvider provider;
  const ProviderCard({super.key, required this.provider});

  @override
  Widget build(BuildContext context) {
    final cardTheme = context.providerCardTheme;
    return Material(
      color: Colors.transparent,
      child: InkWell(
        borderRadius: BorderRadius.circular(8),
        onTap:
            () => Nav.to(
              Pages.provider,
              arguments: ProviderDetailsPageNav(providerId: provider.id),
            ),
        child: Container(
          width: MediaQuery.sizeOf(context).width * .4,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            color: cardTheme.backgroundColor,
            border: Border.fromBorderSide(cardTheme.side),
          ),
          child: Column(
            children: [
              Expanded(
                child: AppImage(
                  path: provider.cover?.medium ?? "",
                  width: double.infinity,
                  decoration: const BoxDecoration(
                    borderRadius: BorderRadius.vertical(
                      top: Radius.circular(8),
                    ),
                  ),
                ),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 8,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            provider.shopName,
                            style: cardTheme.nameStyle,
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        const Gap(8),
                        Container(
                          height: 35,
                          width: 35,
                          decoration: BoxDecoration(
                            color: context.appColorScheme.secondaryColor,
                            shape: BoxShape.circle,
                          ),
                          child: Center(
                            child: Icon(
                              Icons.arrow_forward_ios_rounded,
                              color: context.appColorScheme.onPrimaryColor,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const Gap(8),
                    Row(
                      children: [
                        SvgIcon(
                          Assets.icons.eyeOutlined,
                          size: 18,
                          color: cardTheme.viewsStyle.color,
                        ),
                        const Gap(4),
                        Text(
                          LocaleKeys.n_views.plural(provider.views),
                          style: cardTheme.viewsStyle,
                        ),
                      ],
                    ),
                    const Gap(12),
                    TagsRow(tags: provider.tags),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

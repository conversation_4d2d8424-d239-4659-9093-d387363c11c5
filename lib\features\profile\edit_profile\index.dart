import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:tredo/core/localization/strings.dart';
import 'package:tredo/core/utils/validator.dart';
import 'package:tredo/core/widgets/general/buttons/button.dart';
import 'package:tredo/core/widgets/general/fields/phone.dart';
import 'package:tredo/core/widgets/general/fields/prefix.dart';
import 'package:tredo/core/widgets/general/pages/scaffold.dart';
import 'package:tredo/core/widgets/general/svg_icon.dart';
import 'package:tredo/features/usecases/states_cities/index.dart';

import 'controller.dart';
import 'widgets/image.dart';

class EditProfilePage extends GetView<EditProfilePageController> {
  const EditProfilePage({super.key});

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      appBar: AppBar(),
      body: Form(
        key: controller.formKey,
        child: ListView(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
          children: [
            const EditProfileImage(),
            const Gap(16),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: controller.firstName,
                    validator: Validator.notNull,
                    textInputAction: TextInputAction.next,
                    decoration: InputDecoration(
                      hintText: tr(LocaleKeys.first_name),
                      prefixIcon: FieldPrefix(
                        child: SvgIcon(Assets.icons.person),
                      ),
                    ),
                  ),
                ),
                const Gap(8),
                Expanded(
                  child: TextFormField(
                    controller: controller.lastName,
                    validator: Validator.notNull,
                    textInputAction: TextInputAction.next,
                    decoration: InputDecoration(
                      hintText: tr(LocaleKeys.last_name),
                      prefixIcon: FieldPrefix(
                        child: SvgIcon(Assets.icons.person),
                      ),
                    ),
                  ),
                ),
              ],
            ),
            const Gap(16),
            PhoneField(controller: controller.phone, enabled: false),
            const Gap(16),
            StatesCitiesWidget(
              initialState: controller.stateId,
              initialCity: controller.cityId,
              onStateChanged:
                  (value) =>
                      value != null ? controller.stateId = value.id : null,
              onCityChanged:
                  (value) =>
                      value != null ? controller.cityId = value.id : null,
              isRequired: true,
            ),
            const Gap(16),
            AppElevatedButton(
              onPressed: controller.confirm,
              child: Text(tr(LocaleKeys.save)),
            ),
          ],
        ),
      ),
    );
  }
}

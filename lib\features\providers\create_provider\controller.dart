import 'dart:io';

import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart' hide FormData, MultipartFile;
import 'package:image_picker/image_picker.dart';
import 'package:tredo/core/localization/localization.dart';
import 'package:tredo/core/localization/strings.dart';
import 'package:tredo/core/services/rest_api/rest_api.dart';
import 'package:tredo/core/utils/image_utils.dart';
import 'package:tredo/core/widgets/general/loading/loading.dart';
import 'package:tredo/core/widgets/general/toast/toast.dart';

class CreateProviderPageController extends GetxController {
  final formKey = GlobalKey<FormState>();

  late TextEditingController providerName,
      shopName,
      description,
      phone,
      whatsapp,
      instagram,
      facebook;

  int? cityId, subcategoryId;

  final Rx<String> _image = "".obs;
  String get image => _image.value;
  set image(String value) => _image.value = value;

  pickImage() async {
    final result = await ImagePicker().pickImage(source: ImageSource.gallery);
    if (result == null) return;

    final compressed = await ImageUtils.compressAndGetFile(File(result.path));

    image = compressed ?? result.path;
  }

  @override
  void onInit() {
    providerName = TextEditingController();
    shopName = TextEditingController();
    description = TextEditingController();
    phone = TextEditingController();
    whatsapp = TextEditingController();
    instagram = TextEditingController();
    facebook = TextEditingController();
    super.onInit();
  }

  @override
  void onClose() {
    providerName.dispose();
    shopName.dispose();
    description.dispose();
    phone.dispose();
    whatsapp.dispose();
    instagram.dispose();
    facebook.dispose();
    super.onClose();
  }

  confirm() async {
    if (!formKey.currentState!.validate()) return;

    Loading.show();

    Map<String, dynamic> body = {
      "provider_name": providerName.text,
      "sub_category_id": subcategoryId,
      "city_id": cityId,
      if (image.isNotEmpty) "image": await MultipartFile.fromFile(image),
    };
    for (var locale in AppLocalization.values) {
      body["shop_name[${locale.value}]"] = shopName.text;
      if (description.text.trim().isNotEmpty) {
        body["description[${locale.value}]"] = description.text;
      }
    }
    List socialLinks = [];
    if (phone.text.isNotEmpty) {
      socialLinks.add({'type': 3, "value": phone.text});
    }
    if (facebook.text.isNotEmpty) {
      socialLinks.add({'type': 1, "value": facebook.text});
    }
    if (instagram.text.isNotEmpty) {
      socialLinks.add({'type': 2, "value": instagram.text});
    }

    for (var i = 0; i < socialLinks.length; i++) {
      body['social_links[$i][type]'] = socialLinks[i]['type'];
      body['social_links[$i][value]'] = socialLinks[i]['value'];
    }

    ResponseModel response = await APIService.instance.requestAPI(
      Request(
        endPoint: EndPoints.providers,
        method: RequestMethod.Post,
        body: FormData.fromMap(body),
      ),
    );
    Loading.dispose();

    if (response.success) {
      Get.back();
      Toast.show(
        message: tr(LocaleKeys.provider_creation_success_message),
        status: ToastStatus.success,
      );
    } else {
      Toast.show(message: response.message);
    }
  }
}

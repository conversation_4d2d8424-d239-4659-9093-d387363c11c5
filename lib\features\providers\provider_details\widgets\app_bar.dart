import 'package:flutter/material.dart';
import 'package:tredo/core/widgets/general/buttons/back_button.dart';
import 'package:tredo/core/widgets/image.dart';

class ProviderAppBar extends StatelessWidget {
  final String? backgroundImage;
  const ProviderAppBar({super.key, required this.backgroundImage});

  @override
  Widget build(BuildContext context) {
    return SliverAppBar(
      pinned: true,
      elevation: 0,
      collapsedHeight: 100,
      expandedHeight: 300,
      leading: const Center(child: AppBackButton(withBorder: true)),
      flexibleSpace: Stack(
        children: [
          AppImage(
            path: backgroundImage ?? "",
            width: double.infinity,
            height: 300 + MediaQuery.paddingOf(context).top,
          ),
          Positioned(
            bottom: 0,
            right: 0,
            left: 0,
            child: Container(
              height: 30,
              decoration: BoxDecoration(
                color: Theme.of(context).scaffoldBackgroundColor,
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(40),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

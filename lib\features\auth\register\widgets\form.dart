import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:tredo/core/localization/strings.dart';
import 'package:tredo/core/utils/validator.dart';
import 'package:tredo/core/widgets/general/fields/password.dart';
import 'package:tredo/core/widgets/general/fields/phone.dart';
import 'package:tredo/core/widgets/general/fields/prefix.dart';
import 'package:tredo/core/widgets/general/svg_icon.dart';

import '../../../usecases/states_cities/index.dart';
import '../controller.dart';

class RegisterFormWidget extends GetView<RegisterPageController> {
  const RegisterFormWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Form(
      key: controller.formKey,
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: TextForm<PERSON>ield(
                  controller: controller.firstName,
                  validator: Validator.notNull,
                  textInputAction: TextInputAction.next,
                  decoration: InputDecoration(
                    hintText: tr(LocaleKeys.first_name),
                    prefixIcon: FieldPrefix(
                      child: SvgIcon(Assets.icons.person),
                    ),
                  ),
                ),
              ),
              const Gap(8),
              Expanded(
                child: TextFormField(
                  controller: controller.lastName,
                  validator: Validator.notNull,
                  textInputAction: TextInputAction.next,
                  decoration: InputDecoration(
                    hintText: tr(LocaleKeys.last_name),
                    prefixIcon: FieldPrefix(
                      child: SvgIcon(Assets.icons.person),
                    ),
                  ),
                ),
              ),
            ],
          ),

          const Gap(24),
          PhoneField(controller: controller.phone, showCounter: true),
          const Gap(24),

          StatesCitiesWidget(
            isRequired: true,
            onCityChanged: (value) => controller.cityId = value?.id,
          ),
          const Gap(24),
          PasswordField(controller: controller.password),
          const Gap(24),
          PasswordField(
            controller: controller.confirmPassword,
            validator:
                (value) =>
                    Validator.confirmPassword(value!, controller.password.text),
            hintText: tr(LocaleKeys.confirm_password),
          ),
        ],
      ),
    );
  }
}

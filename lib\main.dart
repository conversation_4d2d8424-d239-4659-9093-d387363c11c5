import 'dart:developer';
import 'dart:io';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:navigation_history_observer/navigation_history_observer.dart';

import 'core/config/app_builder.dart';
import 'core/config/defaults.dart';
import 'core/localization/localization.dart';
import 'core/routes/routes.dart';
import 'core/style/style.dart';

class MyHttpOverrides extends HttpOverrides {
  @override
  HttpClient createHttpClient(SecurityContext? context) {
    return super.createHttpClient(context)
      ..badCertificateCallback =
          (X509Certificate cert, String host, int port) => true;
  }
}

Future<void> main() async {
  log("بسم الله الرحمن الرحيم");

  HttpOverrides.global = MyHttpOverrides();

  WidgetsFlutterBinding.ensureInitialized();
  await EasyLocalization.ensureInitialized();

  runApp(
    EasyLocalization(
      supportedLocales: AppLocalization.values.map((e) => e.locale).toList(),
      path: "assets/translations",
      fallbackLocale: AppLocalization.en.locale,
      child: const MyApp(),
    ),
  );
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    Get.put(AppBuilder());
    return GetMaterialApp(
      debugShowCheckedModeBanner: false,
      title: Default.appTitle,
      theme: AppStyle.lightTheme,
      // localization
      localizationsDelegates: context.localizationDelegates,
      locale: context.locale,
      supportedLocales: context.supportedLocales,
      // routing
      initialRoute: AppRouting.initialroute.name,
      unknownRoute: AppRouting.unknownRoute,
      navigatorObservers: [NavigationHistoryObserver()],
      getPages: AppRouting.routes(),
    );
  }
}

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:tredo/core/assets/assets.gen.dart';
import 'package:tredo/core/localization/strings.dart';
import 'package:tredo/core/models/selection.dart';
import 'package:tredo/core/services/state_management/widgets/obs_widget.dart';
import 'package:tredo/core/utils/validator.dart';
import 'package:tredo/core/widgets/general/fields/dropdown_filed.dart';
import 'package:tredo/core/widgets/general/fields/field_error.dart';
import 'package:tredo/core/widgets/general/fields/field_loading.dart';
import 'package:tredo/core/widgets/general/fields/required_filed.dart';

import '../controller.dart';

class StatesDropdown extends GetView<StatesCitiesController> {
  const StatesDropdown({
    super.key,
    required this.initialState,
    required this.isRequired,
    required this.supportNone,
    required this.onStateChanged,
  });

  final int? initialState;
  final bool isRequired;
  final bool supportNone;
  final void Function(Selection? value)? onStateChanged;

  @override
  Widget build(BuildContext context) {
    return ObsListBuilder(
      obs: controller.states,
      loader:
          (context) =>
              isRequired
                  ? const RequiredField(child: FieldLoadingWidget())
                  : const FieldLoadingWidget(),
      errorBuilder:
          (context, error) =>
              isRequired
                  ? RequiredField(
                    child: FieldErrorWidget(
                      error: error,
                      onRefresh: controller.refreshStates,
                    ),
                  )
                  : FieldErrorWidget(
                    error: error,
                    onRefresh: controller.refreshStates,
                  ),
      builder: (context, states) {
        return AppDropdownField(
          hint: tr(LocaleKeys.state),
          value: states.firstWhereOrNull(
            (element) => element.id == initialState,
          ),
          validator: isRequired ? Validator.notNull : null,
          icon: Assets.icons.earth,
          selections: states,
          supportNone: supportNone,
          onChanged: (value) {
            onStateChanged?.call(value);
            controller.selectedStateId = value?.id ?? Selection.none.id;
          },
        );
      },
    );
  }
}

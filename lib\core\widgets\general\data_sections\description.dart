import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:readmore/readmore.dart';
import 'package:tredo/core/localization/strings.dart';
import 'package:tredo/core/style/utils/context.dart';

class DescriptionSection extends StatelessWidget {
  const DescriptionSection({super.key, required this.description});

  final String description;

  @override
  Widget build(BuildContext context) {
    if (description.trim().isEmpty) return const SizedBox();
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(tr(LocaleKeys.description), style: context.textTheme.titleLarge),
        const Gap(12),
        ReadMoreText(
          description,
          style: context.textTheme.bodyMedium,
          trimMode: TrimMode.Line,
          trimLines: 5,
        ),
      ],
    );
  }
}

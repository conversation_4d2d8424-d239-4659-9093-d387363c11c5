import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:get/get.dart';
import 'package:tredo/core/constants/controllers_tags.dart';
import 'package:tredo/core/localization/strings.dart';
import 'package:tredo/core/models/category/category.dart';
import 'package:tredo/core/routes/navigation.dart';
import 'package:tredo/core/services/pagination/options/grid_view.dart';
import 'package:tredo/core/widgets/general/pages/scaffold.dart';
import 'package:tredo/features/categories/widgets/category_card/category_card.dart';
import 'package:tredo/features/providers/category_providers/models/nav.dart';

import 'controller.dart';

class CategoriesPage extends GetView<CategoriesPageController> {
  const CategoriesPage({super.key});

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      appBar: AppBar(title: Text(tr(LocaleKeys.categories))),
      body: GridViewPagination.aligned(
        tag: ControllersTags.categories_pager,
        fetchApi: controller.fetchCategories,
        fromJson: Category.fromJson,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
        crossAxisCount: 4,
        mainAxisSpacing: 16,
        crossAxisSpacing: 12,
        initialLoading: AlignedGridView.count(
          itemCount: 24,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
          crossAxisCount: 4,
          mainAxisSpacing: 16,
          crossAxisSpacing: 12,
          itemBuilder: (context, index) => const CategoryCardLoading(),
        ),
        loading: const CategoryCardLoading(),
        itemBuilder:
            (context, index, category) => SizedBox(
              height: 90,
              child: CategoryCard(
                category: category,
                onTap:
                    () => Nav.to(
                      Pages.category_providers,
                      arguments: CategoryProvidersPageNav(category: category),
                    ),
              ),
            ),
      ),
    );
  }
}

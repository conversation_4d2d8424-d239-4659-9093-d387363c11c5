import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../core/widgets/image.dart';

class MediaPreview extends StatelessWidget {
  final List<String> media;
  final int initialIndex;
  const MediaPreview({super.key, required this.media, this.initialIndex = 0});

  static preview({required List<String> media, int initialIndex = 0}) =>
      Get.dialog(
        MediaPreview(media: media, initialIndex: initialIndex),
        barrierColor: Colors.transparent,
      );

  @override
  Widget build(BuildContext context) {
    return Dismissible(
      key: GlobalKey(),
      direction: DismissDirection.vertical,
      onDismissed: (_) => Get.back(),
      child: Container(
        decoration: const BoxDecoration(color: Colors.black),
        child: PageView.builder(
          controller: PageController(
            initialPage: initialIndex,
            viewportFraction: 1.1,
          ),
          itemCount: media.length,
          itemBuilder: (context, index) {
            return Padding(
              padding: EdgeInsets.symmetric(horizontal: Get.width * .05),
              child: AppImage(
                path: media[index],
                type: ImageType.CachedNetwork,
                width: double.infinity,
                fit: BoxFit.contain,
              ),
            );
          },
        ),
      ),
    );
  }
}

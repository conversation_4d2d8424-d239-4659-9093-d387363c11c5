import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:tredo/core/localization/strings.dart';
import 'package:tredo/core/widgets/general/buttons/button.dart';
import 'package:tredo/core/widgets/general/fields/phone.dart';
import 'package:tredo/core/widgets/general/pages/scaffold.dart';

import '../widgets/appbar.dart';
import 'controller.dart';

class ForgetPasswordPage extends GetView<ForgetPasswordPageController> {
  const ForgetPasswordPage({super.key});

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      appBar: AuthAppBar(title: Text(tr(LocaleKeys.forget_password))),
      body: ListView(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
        children: [
          PhoneField(
            controller: controller.phone,
            showCounter: true,
            onValidityChange: (isValid) => controller.isPhoneValid = isValid,
          ),
          const Gap(36),
          Obx(
            () => AppElevatedButton(
              onPressed: controller.isPhoneValid ? controller.sendCode : null,
              child: Text(tr(LocaleKeys.send_code)),
            ),
          ),
        ],
      ),
    );
  }
}

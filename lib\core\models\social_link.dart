import 'package:easy_localization/easy_localization.dart';
import 'package:tredo/core/localization/strings.dart';
import 'package:tredo/core/widgets/general/svg_icon.dart';

enum SocialLinkType {
  phone,
  facebook,
  instagram,
  other;

  static SocialLinkType fromString(String value) => switch (value
      .toLowerCase()) {
    "phone" => phone,
    "facebook" => facebook,
    "instagram" => instagram,
    _ => other,
  };

  SvgGenImage get icon => switch (this) {
    phone => Assets.icons.phone,
    facebook => Assets.icons.facebook,
    instagram => Assets.icons.instagram,
    _ => Assets.icons.earth,
  };

  String? get text => switch (this) {
    phone => tr(LocaleKeys.phone_number),
    facebook => tr(LocaleKeys.facebook),
    instagram => tr(LocaleKeys.instagram),
    _ => null,
  };
}

class SocialLink {
  SocialLinkType type;
  String value;

  SocialLink({required this.type, required this.value});

  SocialLink copyWith({SocialLinkType? type, String? value}) =>
      SocialLink(type: type ?? this.type, value: value ?? this.value);

  factory SocialLink.fromJson(Map<String, dynamic> json) => SocialLink(
    type: SocialLinkType.fromString(
      json["type_case_name"] ?? json['key_case_name'],
    ),
    value: json["value"],
  );

  Map<String, dynamic> toJson() => {"type_case_name": type, "value": value};
}

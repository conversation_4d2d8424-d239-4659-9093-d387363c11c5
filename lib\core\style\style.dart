// ignore_for_file: annotate_overrides

import 'package:flutter/material.dart';
import 'package:theme_tailor_annotation/theme_tailor_annotation.dart';
import 'package:tredo/core/assets/fonts.gen.dart';
import 'package:pinput/pinput.dart';
import 'package:tredo/features/entities/widgets/entity_card/style/style.dart';
import 'package:tredo/features/offers/widgets/offer_card/style/style.dart';
import 'package:tredo/features/providers/widgets/provider_card/style/style.dart';
import 'package:tredo/features/providers/widgets/provider_tile/style/style.dart';

import '../../features/categories/widgets/category_card/style/style.dart';
import 'repo.dart';

part 'style.tailor.dart';

const _kFieldRadius = BorderRadius.all(Radius.circular(7));

abstract class AppStyle {
  static ThemeData get lightTheme {
    AppColorScheme colorScheme = AppColorScheme.light();

    TextTheme textTheme = const TextTheme(
      displayLarge: TextStyle(fontSize: 57),
      displayMedium: TextStyle(fontSize: 45),
      displaySmall: TextStyle(fontSize: 36),

      headlineLarge: TextStyle(fontSize: 32),
      headlineMedium: TextStyle(fontSize: 28),
      headlineSmall: TextStyle(fontSize: 24),

      titleLarge: TextStyle(fontSize: 22),
      titleMedium: TextStyle(fontSize: 16),
      titleSmall: TextStyle(fontSize: 14),

      bodyLarge: TextStyle(fontSize: 16),
      bodyMedium: TextStyle(fontSize: 14),
      bodySmall: TextStyle(fontSize: 12),

      labelLarge: TextStyle(fontSize: 14),
      labelMedium: TextStyle(fontSize: 12),
      labelSmall: TextStyle(fontSize: 11),
    );

    return ThemeData(
      extensions: [
        colorScheme,
        PinputTheme.light(colorScheme),
        CategoryCardTheme.light(textTheme),
        EntityCardTheme.light(colorScheme, textTheme),
        OfferCardTheme.light(colorScheme, textTheme),
        ProviderTileTheme.light(textTheme),
        ProviderCardTheme.light(textTheme),
      ],
      textTheme: textTheme,
      fontFamily: FontFamily.lato,
      primaryColor: colorScheme.primaryColor,
      // scaffoldBackgroundColor: colorScheme.onPrimaryColor,
      colorScheme: ColorScheme.fromSwatch(
        primarySwatch: colorScheme.primaryColor,
        backgroundColor: StyleRepo.purple.shade50,
        errorColor: colorScheme.errorColor,
        accentColor: colorScheme.secondaryColor,
      ),
      dividerTheme: const DividerThemeData(color: StyleRepo.grey),
      inputDecorationTheme: InputDecorationTheme(
        constraints: const BoxConstraints(minHeight: 50),
        contentPadding: const EdgeInsets.symmetric(
          vertical: 12,
          horizontal: 16,
        ),
        errorStyle: textTheme.labelMedium!.copyWith(
          color: colorScheme.errorColor,
        ),
        hintStyle: textTheme.bodyMedium!.copyWith(color: StyleRepo.grey),
        border: const OutlineInputBorder(borderRadius: _kFieldRadius),
        enabledBorder: const OutlineInputBorder(
          borderRadius: _kFieldRadius,
          borderSide: BorderSide(color: StyleRepo.grey),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: _kFieldRadius,
          borderSide: BorderSide(color: StyleRepo.violate.shade400, width: 1.5),
        ),
        disabledBorder: OutlineInputBorder(
          borderRadius: _kFieldRadius,
          borderSide: BorderSide(color: StyleRepo.purple.shade600),
        ),
        errorBorder: const OutlineInputBorder(
          borderRadius: _kFieldRadius,
          borderSide: BorderSide(color: StyleRepo.red),
        ),
        focusedErrorBorder: const OutlineInputBorder(
          borderRadius: _kFieldRadius,
          borderSide: BorderSide(color: StyleRepo.red, width: 1.5),
        ),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ButtonStyle(
          backgroundColor: WidgetStateProperty.resolveWith((states) {
            if (states.contains(WidgetState.disabled)) {
              return colorScheme.disabledColor;
            }
            return colorScheme.primaryColor;
          }),
          foregroundColor: WidgetStatePropertyAll(colorScheme.onPrimaryColor),
          overlayColor: WidgetStateColor.resolveWith((states) {
            return colorScheme.onPrimaryColor.withValues(alpha: .5);
          }),
          shape: WidgetStatePropertyAll(
            RoundedRectangleBorder(borderRadius: BorderRadius.circular(7)),
          ),
        ),
      ),
      textButtonTheme: TextButtonThemeData(
        style: ButtonStyle(
          textStyle: WidgetStatePropertyAll(textTheme.titleMedium!),
          foregroundColor: WidgetStateProperty.resolveWith((states) {
            if (states.contains(WidgetState.disabled)) {
              return colorScheme.disabledColor;
            }
            return colorScheme.secondaryColor;
          }),
        ),
      ),
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: ButtonStyle(
          textStyle: WidgetStatePropertyAll(textTheme.titleMedium!),
          foregroundColor: WidgetStateProperty.resolveWith((states) {
            if (states.contains(WidgetState.disabled)) {
              return colorScheme.disabledColor;
            }
            return colorScheme.secondaryColor;
          }),
          side: WidgetStateProperty.resolveWith((states) {
            if (states.contains(WidgetState.disabled)) {
              return BorderSide(color: colorScheme.disabledColor);
            }
            return BorderSide(color: colorScheme.secondaryColor);
          }),
          shape: WidgetStatePropertyAll(
            RoundedRectangleBorder(borderRadius: BorderRadius.circular(7)),
          ),
        ),
      ),
      chipTheme: ChipThemeData(
        backgroundColor: StyleRepo.purple.shade600,
        labelStyle: textTheme.labelMedium!.copyWith(
          color: StyleRepo.purple.shade50,
        ),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(4)),
        padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 2),
      ),
      appBarTheme: AppBarTheme(
        backgroundColor: Colors.transparent,
        elevation: 0,
        actionsIconTheme: IconThemeData(color: colorScheme.onPrimaryColor),
        centerTitle: true,
        foregroundColor: colorScheme.onPrimaryColor,
        toolbarHeight: 100,
        titleTextStyle: textTheme.headlineSmall,
      ),
      highlightColor: colorScheme.secondaryColor.shade100,
      splashColor: colorScheme.secondaryColor.shade300.withValues(alpha: .1),
    );
  }

  static ThemeData get darkTheme {
    return ThemeData();
  }
}

@TailorMixin()
class AppColorScheme extends ThemeExtension<AppColorScheme>
    with _$AppColorSchemeTailorMixin {
  AppColorScheme({
    required this.primaryColor,
    required this.secondaryColor,
    required this.errorColor,
    required this.onPrimaryColor,
    required this.onErrorColor,
    required this.disabledColor,
    required this.successColor,
  });

  MaterialColor primaryColor;
  Color onPrimaryColor;
  MaterialColor secondaryColor;
  Color errorColor;
  Color onErrorColor;
  Color disabledColor;
  Color successColor;

  factory AppColorScheme.light() => AppColorScheme(
    primaryColor: StyleRepo.blue,
    secondaryColor: StyleRepo.violate,
    errorColor: StyleRepo.red,
    onPrimaryColor: StyleRepo.purple.shade50,
    onErrorColor: StyleRepo.purple.shade50,
    disabledColor: StyleRepo.grey,
    successColor: StyleRepo.green,
  );
}

@TailorMixin()
class PinputTheme extends ThemeExtension<PinputTheme>
    with _$PinputThemeTailorMixin {
  final PinTheme defaultTheme;
  final PinTheme focused;
  final PinTheme submitted;
  final PinTheme error;

  static const BoxConstraints _kBoxConstraints = BoxConstraints(
    minWidth: 60,
    minHeight: 60,
    maxHeight: 70,
    maxWidth: 70,
  );

  static const _kRadius = 16.0;

  static const _kTextStyle = TextStyle(
    fontSize: 20,
    fontWeight: FontWeight.w600,
  );

  PinputTheme({
    required this.defaultTheme,
    required this.focused,
    required this.submitted,
    required this.error,
  });

  factory PinputTheme.light(AppColorScheme colorScheme) => PinputTheme(
    defaultTheme: PinTheme(
      decoration: BoxDecoration(
        color: StyleRepo.purple.shade50,
        border: Border.all(color: StyleRepo.grey),
        borderRadius: BorderRadius.circular(_kRadius),
      ),
      constraints: _kBoxConstraints,
      textStyle: _kTextStyle,
    ),
    submitted: PinTheme(
      decoration: BoxDecoration(
        color: colorScheme.secondaryColor.shade50,
        border: Border.all(color: colorScheme.secondaryColor),
        borderRadius: BorderRadius.circular(_kRadius),
      ),
      constraints: _kBoxConstraints,
      textStyle: _kTextStyle,
    ),
    focused: PinTheme(
      decoration: BoxDecoration(
        color: StyleRepo.purple.shade50,
        border: Border.all(color: colorScheme.secondaryColor, width: 2),
        borderRadius: BorderRadius.circular(_kRadius),
      ),
      constraints: _kBoxConstraints,
      textStyle: _kTextStyle,
    ),
    error: PinTheme(
      decoration: BoxDecoration(
        color: colorScheme.onErrorColor,
        border: Border.all(color: colorScheme.errorColor, width: 2),
        borderRadius: BorderRadius.circular(_kRadius),
      ),
      constraints: _kBoxConstraints,
      textStyle: _kTextStyle,
    ),
  );
}

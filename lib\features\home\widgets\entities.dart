import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:tredo/core/services/state_management/widgets/obs_widget.dart';
import 'package:tredo/core/widgets/general/error/error_card.dart';
import 'package:tredo/features/entities/widgets/entity_card/entity_card.dart';

import '../controller.dart';

class EntitiesList extends GetView<HomePageController> {
  const EntitiesList({super.key});

  @override
  Widget build(BuildContext context) {
    return ObsListBuilder(
      obs: controller.entities,
      loader:
          (context) => SizedBox(
            height: 65,
            child: ListView.separated(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: 10,
              separatorBuilder: (_, __) => const Gap(12),
              itemBuilder: (context, index) => const EntityCardLoading(),
            ),
          ),
      errorBuilder:
          (context, error) => Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: SizedBox(
              height: 65,
              child: ErrorCard(
                error: error,
                onRefresh: controller.refreshEntities,
                borderRadius: BorderRadius.circular(15),
              ),
            ),
          ),
      builder: (context, entities) {
        return SizedBox(
          height: 80,
          child: ListView.separated(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: entities.length,
            separatorBuilder: (_, __) => const Gap(12),
            itemBuilder:
                (context, index) => EntityCard(entity: entities[index]),
          ),
        );
      },
    );
  }
}

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:tredo/core/localization/strings.dart';
import 'package:tredo/core/models/provider/main_provider.dart';
import 'package:tredo/core/routes/navigation.dart';
import 'package:tredo/core/style/style.dart';
import 'package:tredo/core/widgets/general/svg_icon.dart';
import 'package:tredo/core/widgets/general/tags_row.dart';
import 'package:tredo/core/widgets/image.dart';
import 'package:tredo/features/providers/provider_details/models/nav.dart';
import 'style/style.dart';

class ProviderTile extends StatelessWidget {
  final MainProvider provider;
  const ProviderTile({super.key, required this.provider});

  @override
  Widget build(BuildContext context) {
    final cardTheme = context.providerTileTheme;
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap:
            () => Nav.to(
              Pages.provider,
              arguments: ProviderDetailsPageNav(providerId: provider.id),
            ),
        borderRadius: BorderRadius.circular(13),
        child: Container(
          height: 115,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            border: Border.fromBorderSide(cardTheme.side),
            borderRadius: BorderRadius.circular(13),
          ),
          child: Row(
            children: [
              AspectRatio(
                aspectRatio: 1,
                child: AppImage(
                  path: provider.cover?.small ?? "",
                  decoration: BoxDecoration(
                    boxShadow: cardTheme.imageShadow,
                    borderRadius: BorderRadius.circular(9),
                  ),
                ),
              ),
              const Gap(12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    Text(
                      provider.shopName,
                      style: cardTheme.nameStyle,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    Row(
                      children: [
                        SvgIcon(
                          Assets.icons.eyeOutlined,
                          size: 18,
                          color: cardTheme.viewsStyle.color,
                        ),
                        const Gap(4),
                        Text(
                          LocaleKeys.n_views.plural(provider.views),
                          style: cardTheme.viewsStyle,
                        ),
                      ],
                    ),
                    TagsRow(tags: provider.tags),
                  ],
                ),
              ),
              Container(
                height: 35,
                width: 35,
                decoration: BoxDecoration(
                  color: context.appColorScheme.secondaryColor,
                  shape: BoxShape.circle,
                ),
                child: Center(
                  child: Icon(
                    Icons.arrow_forward_ios_rounded,
                    color: context.appColorScheme.onPrimaryColor,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

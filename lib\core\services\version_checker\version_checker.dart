import 'package:firebase_database/firebase_database.dart';

//singlton
class VersionChecker {
  static final VersionChecker instance = VersionChecker._internal();
  factory VersionChecker() => instance;
  VersionChecker._internal();

  FirebaseStore firebaseDatabase = FirebaseDatabase.instance;

  init() {
    _read();
  }

  // read the value from firebase
  // the value is boolean
  // the key is current version
  // "1.0.0": true
  Future _read() async {
    firebaseDatabase
  //   try {
  //     final ref = firebaseDatabase.ref("version");
  //     final snapshot = await ref.get();
  //     if (snapshot.exists) {
  //       return snapshot.value;
  //     } else {
  //       return null;
  //     }
  //   } catch (e) {
  //     print(e);
  //   }
  // }
}

import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart' hide MultipartFile, FormData;
import 'package:image_picker/image_picker.dart';
import 'package:tredo/core/config/app_builder.dart';
import 'package:tredo/core/models/user/general_user.dart';
import 'package:tredo/core/services/rest_api/rest_api.dart';
import 'package:tredo/core/utils/image_utils.dart';
import 'package:tredo/core/widgets/general/loading/loading.dart';
import 'package:tredo/core/widgets/general/toast/toast.dart';

import 'models/nav.dart';

class EditProfilePageController extends GetxController {
  final EditProfilePageNav nav;
  EditProfilePageController(this.nav);

  AppBuilder appBuilder = Get.find();

  final formKey = GlobalKey<FormState>();

  late int stateId, cityId;

  late TextEditingController firstName, lastName, phone;

  late final Rx<String> _image;
  String get image => _image.value;
  set image(String value) => _image.value = value;

  @override
  void onInit() {
    stateId = nav.user.state.id;
    cityId = nav.user.city.id;
    firstName = TextEditingController(text: nav.user.firstName);
    lastName = TextEditingController(text: nav.user.lastName);
    phone = TextEditingController(text: nav.user.phone);
    _image = (nav.user.image).obs;
    super.onInit();
  }

  @override
  void onClose() {
    firstName.dispose();
    lastName.dispose();
    phone.dispose();
    super.onClose();
  }

  pickImage() async {
    final result = await ImagePicker().pickImage(source: ImageSource.gallery);
    if (result == null) return;

    final compressed = await ImageUtils.compressAndGetFile(File(result.path));

    image = compressed ?? result.path;
  }

  Future confirm() async {
    if (!formKey.currentState!.validate()) return;

    Loading.show();

    Map<String, dynamic> body = {
      "first_name": firstName.text,
      "last_name": lastName.text,
      "city_id": cityId,
      if (image.isNotEmpty && !image.startsWith("http"))
        "image": await MultipartFile.fromFile(image),
    };

    ResponseModel response = await APIService.instance.requestAPI(
      Request(
        endPoint: EndPoints.update_profile,
        method: RequestMethod.Post,
        body: FormData.fromMap(body),
      ),
    );
    Loading.dispose();

    if (response.success) {
      appBuilder.setUser(GeneralUser.fromJson(response.data));
      Get.back();
    } else {
      Toast.show(message: response.message);
    }
  }
}

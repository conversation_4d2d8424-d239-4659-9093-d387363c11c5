import 'package:flutter/material.dart';
import 'package:tredo/core/assets/assets.gen.dart';

class EmptyWidget extends StatelessWidget {
  const EmptyWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(child: Assets.icons.noData.svg());
  }
}

class SliverEmptyWidget extends StatelessWidget {
  const SliverEmptyWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return SliverList(delegate: SliverChildListDelegate([const EmptyWidget()]));
  }
}

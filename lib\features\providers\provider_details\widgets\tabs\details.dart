import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:tredo/core/localization/strings.dart';
import 'package:tredo/core/widgets/general/data_sections/contact_details.dart';
import 'package:tredo/core/widgets/general/data_sections/description.dart';

import '../../models/provider.dart';

class DetailsTab extends StatelessWidget {
  final ProviderDetails provider;
  const DetailsTab({super.key, required this.provider});

  @override
  Widget build(BuildContext context) {
    return SliverPadding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      sliver: SliverList(
        delegate: SliverChildListDelegate([
          DescriptionSection(description: provider.description),
          const Gap(16),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: provider.tags.map((e) => Chip(label: Text(e))).toList(),
          ),
          const Gap(16),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                tr(LocaleKeys.contact_details),
                style: context.textTheme.titleLarge,
              ),
              const Gap(12),
              ContactDetails(links: provider.links),
            ],
          ),
          const Gap(16),
        ]),
      ),
    );
  }
}

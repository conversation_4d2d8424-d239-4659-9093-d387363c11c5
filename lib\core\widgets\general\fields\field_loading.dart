import 'package:flutter/material.dart';
import 'package:tredo/core/style/style.dart';

import '../loading/shimmer_loading.dart';

class FieldLoadingWidget extends StatelessWidget {
  const FieldLoadingWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return ShimmerWidget(
      borderRadius: BorderRadius.circular(7),
      child: Container(
        height: 50,
        width: double.infinity,
        color: context.appColorScheme.onPrimaryColor,
      ),
    );
  }
}

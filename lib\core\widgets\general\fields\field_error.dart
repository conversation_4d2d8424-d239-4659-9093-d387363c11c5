import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:tredo/core/style/style.dart';

class FieldErrorWidget extends StatelessWidget {
  final String error;
  final void Function()? onRefresh;
  const FieldErrorWidget({super.key, required this.error, this.onRefresh});

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        if (onRefresh != null)
          IconButton(onPressed: onRefresh, icon: const Icon(Icons.refresh)),
        if (onRefresh != null) const Gap(12),
        Expanded(
          child: Text(
            error,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: context.appColorScheme.errorColor,
            ),
          ),
        ),
      ],
    );
  }
}

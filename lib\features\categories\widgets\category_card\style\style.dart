// ignore_for_file: annotate_overrides

import 'package:flutter/material.dart';
import 'package:theme_tailor_annotation/theme_tailor_annotation.dart';
import 'package:tredo/core/style/repo.dart';

part 'style.tailor.dart';

@TailorMixin()
class CategoryCardTheme extends ThemeExtension<CategoryCardTheme>
    with _$CategoryCardThemeTailorMixin {
  CategoryCardTheme({
    required this.backgroundColor,
    required this.foregroundColor,
    required this.style,
  });

  final Color backgroundColor;
  final Color foregroundColor;
  final TextStyle style;

  factory CategoryCardTheme.light(TextTheme textTheme) => CategoryCardTheme(
    backgroundColor: StyleRepo.purple.shade200,
    foregroundColor: StyleRepo.purple.shade600,
    style: textTheme.bodyMedium!.copyWith(color: StyleRepo.purple.shade600),
  );
}

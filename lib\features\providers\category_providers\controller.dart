import 'package:dio/dio.dart';
import 'package:get/get.dart';
import 'package:tredo/core/models/category/category.dart';
import 'package:tredo/core/services/pagination/controller.dart';
import 'package:tredo/core/services/rest_api/rest_api.dart';
import 'package:tredo/core/services/state_management/obs.dart';
import 'package:tredo/core/utils/debouncer.dart';

import '../../usecases/filters/index.dart';
import '../../usecases/filters/models/filter_result.dart';
import '../../usecases/filters/models/filters_params.dart';
import 'models/nav.dart';

class CategoryProvidersPageController extends GetxController {
  final CategoryProvidersPageNav nav;
  CategoryProvidersPageController(this.nav);

  FiltersResult filters = FiltersResult();

  late PaginationController pagerController;

  @override
  onInit() {
    fetchSubcaetgories();
    selectingCategoriesDebouncer = Debouncer(
      execute: (_) => pagerController.refreshData(),
    );
    super.onInit();
  }

  onFilter() async {
    final result = await FiltersBottomSheet.showBottomSheet(
      params: FiltersParams(
        initialFilters: filters.toInitialFilters(),
        activations: const FiltersActivations(categories: false),
      ),
    );
    if (result == null) return null;

    filters = result;
    pagerController.refreshData();
  }

  String search = '';
  onSearch(String search) {
    this.search = search;
    pagerController.refreshData();
  }

  Future<ResponseModel> fetchProviders(int page, CancelToken cancel) async {
    return await APIService.instance.requestAPI(
      Request(
        endPoint: EndPoints.providers,
        params: {
          'page': page,
          if (search.trim().isNotEmpty) "name": search,
          'category_id': nav.category.id,
          if (selectedSubcategoryId != 0)
            'sub_category_id': selectedSubcategoryId,
          if (filters.stateId != null) 'state_id': filters.stateId,
          if (filters.cityId != null) 'city_id': filters.cityId,
        },
        cancelToken: cancel,
      ),
    );
  }

  //SECTION - Subcategories
  ObsList<Category> subcategories = ObsList([]);

  fetchSubcaetgories() async {
    ResponseModel response = await APIService.instance.requestAPI(
      Request(
        endPoint: EndPoints.subcategories,
        params: {"category_ids[0]": nav.category.id},
        fromJson: Category.fromJson,
      ),
    );
    if (response.success) {
      subcategories.value = response.data;
    } else {
      subcategories.error = response.message;
    }
  }

  refreshSubcategories() async {
    subcategories.reset();
    await fetchSubcaetgories();
  }

  late Debouncer selectingCategoriesDebouncer;

  final Rx<int> _selectedSubcategoryId = 0.obs;
  int get selectedSubcategoryId => _selectedSubcategoryId.value;
  set selectedSubcategoryId(int value) {
    _selectedSubcategoryId.value = value;
    selectingCategoriesDebouncer.add(value);
  }

  //!SECTION
}

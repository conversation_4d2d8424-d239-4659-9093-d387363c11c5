// ignore_for_file: constant_identifier_names

/// A reference for all controllers tags that used in dependecy injection
abstract class ControllersTags {
  static const tag = '';

  static const pager_tag = '';

  static const providers_pager = 'providers_pager';
  static const categories_pager = 'categories_pager';
  static const entities_pager = 'entities_pager';
  static const offers_pager = 'offers_pager';
  static const provider_offers_pager = 'provider_offers_pager';
  static const category_providers_pager = 'category_providers_pager';
  static const notificatoins_pager = 'notificatoins_pager';
}

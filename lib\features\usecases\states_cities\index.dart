import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:tredo/core/models/selection.dart';

import 'controller.dart';
import 'widgets/cities.dart';
import 'widgets/states.dart';

class StatesCitiesWidget extends StatelessWidget {
  final int? initialState;
  final int? initialCity;
  final void Function(Selection? value)? onStateChanged;
  final void Function(Selection? value)? onCityChanged;
  final bool supportNone;
  final bool isRequired;

  const StatesCitiesWidget({
    super.key,
    this.initialState,
    this.initialCity,
    this.onStateChanged,
    this.onCityChanged,
    this.supportNone = false,
    this.isRequired = false,
  });

  @override
  Widget build(BuildContext context) {
    Get.put(
      StatesCitiesController(
        initialCity: initialCity,
        initialState: initialState,
      ),
    );

    return Row(
      spacing: 12,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: StatesDropdown(
            initialState: initialState,
            isRequired: isRequired,
            supportNone: supportNone,
            onStateChanged: onStateChanged,
          ),
        ),
        Expanded(
          child: CitiesDropdown(
            onCityChanged: onCityChanged,
            isRequired: isRequired,
            supportNone: supportNone,
          ),
        ),
      ],
    );
  }
}

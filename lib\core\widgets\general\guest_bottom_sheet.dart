import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:tredo/core/assets/assets.gen.dart';
import 'package:tredo/core/localization/strings.dart';

import '../../config/app_builder.dart';
import 'buttons/button.dart';

class GuestBottomSheet extends StatelessWidget {
  const GuestBottomSheet({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: Get.width,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 36),
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(45),
          topRight: Radius.circular(45),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Assets.icons.logo.svg(height: 100),
          const Gap(24),
          Text(
            tr(LocaleKeys.guest_message),
            textAlign: TextAlign.center,
            style: const TextStyle(fontWeight: FontWeight.w600, fontSize: 16),
          ),
          const Gap(24),
          AppElevatedButton(
            onPressed: () => Get.find<AppBuilder>().logout(),
            child: Text(tr(LocaleKeys.sign_up)),
          ),
        ],
      ),
    );
  }
}

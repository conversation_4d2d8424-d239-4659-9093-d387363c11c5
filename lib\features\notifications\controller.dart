import 'package:dio/dio.dart';
import 'package:get/get.dart';
import 'package:tredo/core/services/rest_api/rest_api.dart';

class NotificationsPageController extends GetxController {
  Future<ResponseModel> fetchNotifications(int page, CancelToken cancel) async {
    return await APIService.instance.requestAPI(
      Request(
        endPoint: EndPoints.notifications,
        params: {"page": page},
        cancelToken: cancel,
      ),
    );
  }
}

import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:tredo/core/widgets/general/buttons/back_button.dart';
import 'package:tredo/core/widgets/general/error/error_card.dart';

import '../controller.dart';

class ProviderDetailsPageError extends GetView<ProviderDetailsPageController> {
  final String error;
  const ProviderDetailsPageError({super.key, required this.error});

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Column(
        children: [
          const Align(
            alignment: AlignmentDirectional.topStart,
            child: Padding(
              padding: EdgeInsets.all(8.0),
              child: AppBackButton(withBorder: true, withShadow: false),
            ),
          ),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
              child: Error<PERSON>ard(
                error: error,
                onRefresh: controller.refreshData,
                borderRadius: BorderRadius.circular(15),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

import '../image.dart';

class MainProvider {
  int id;
  String shopName;
  int views;
  ImageModel? cover;
  List<String> tags;

  MainProvider({
    required this.id,
    required this.shopName,
    required this.views,
    required this.tags,
    this.cover,
  });

  MainProvider copyWith({
    int? id,
    String? shopName,
    int? views,
    ImageModel? cover,
    List<String>? tags,
  }) => MainProvider(
    id: id ?? this.id,
    shopName: shopName ?? this.shopName,
    views: views ?? this.views,
    cover: cover ?? this.cover,
    tags: tags ?? this.tags,
  );

  factory MainProvider.fromJson(Map<String, dynamic> json) {
    bool emptyMedia = json['media'] == null || json['media'] is List;
    return MainProvider(
      id: json["id"],
      shopName: json["shop_name"],
      views: json["views"],
      cover: emptyMedia ? null : ImageModel.fromJson(json["media"]['image'][0]),
      tags:
          json["tags"] != null
              ? List<String>.from(json["tags"].map((x) => x['name']))
              : [],
    );
  }
  Map<String, dynamic> toJson() => {
    "id": id,
    "shop_name": shopName,
    "views": views,
    "cover": cover?.to<PERSON>son(),
    "tags": List<dynamic>.from(tags.map((x) => {"name": x})),
  };
}

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tredo/core/localization/strings.dart';
import 'package:tredo/core/services/rest_api/rest_api.dart';
import 'package:tredo/core/widgets/general/toast/toast.dart';
import 'package:tredo/core/widgets/general/loading/loading.dart';

import 'models/nav.dart';

class ResetPasswordPageController extends GetxController {
  final ResetPasswordPageNav nav;
  ResetPasswordPageController(this.nav);

  final formKey = GlobalKey<FormState>();

  late TextEditingController firstPassword, secondPassword;

  @override
  void onInit() {
    firstPassword = TextEditingController();
    secondPassword = TextEditingController();
    super.onInit();
  }

  @override
  void onClose() {
    firstPassword.dispose();
    secondPassword.dispose();
    super.onClose();
  }

  resetPassword() async {
    if (!formKey.currentState!.validate()) return;

    Loading.show();

    ResponseModel response = await APIService.instance.requestAPI(
      Request(
        endPoint:
            nav.mode == ResetPasswordMode.reset
                ? EndPoints.reset_password
                : EndPoints.change_password,
        method: RequestMethod.Post,
        body:
            nav.mode == ResetPasswordMode.reset
                ? {
                  "phone": nav.phone,
                  "code": nav.code,

                  "password": firstPassword.text,
                  "password_confirmation": secondPassword.text,
                }
                : {
                  "old_password": firstPassword.text,
                  "new_password": secondPassword.text,
                  "new_password_confirmation": secondPassword.text,
                },
      ),
    );

    Loading.dispose();

    if (response.success) {
      Get.back(result: true);
      Toast.show(
        message: tr(LocaleKeys.password_changed_successfully),
        status: ToastStatus.success,
      );
    } else {
      Toast.show(message: response.message);
    }
  }
}

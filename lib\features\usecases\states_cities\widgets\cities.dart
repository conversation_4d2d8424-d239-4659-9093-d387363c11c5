import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:tredo/core/assets/assets.gen.dart';
import 'package:tredo/core/localization/strings.dart';
import 'package:tredo/core/models/selection.dart';
import 'package:tredo/core/services/state_management/widgets/obs_widget.dart';
import 'package:tredo/core/utils/validator.dart';
import 'package:tredo/core/widgets/general/fields/dropdown_filed.dart';
import 'package:tredo/core/widgets/general/fields/field_error.dart';
import 'package:tredo/core/widgets/general/fields/field_loading.dart';
import 'package:tredo/core/widgets/general/fields/required_filed.dart';

import '../controller.dart';

class CitiesDropdown extends GetView<StatesCitiesController> {
  const CitiesDropdown({
    super.key,
    required this.onCityChanged,
    required this.isRequired,
    required this.supportNone,
  });

  final void Function(Selection? value)? onCityChanged;
  final bool isRequired;
  final bool supportNone;

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (controller.selectedCityId == Selection.none.id) {
        onCityChanged?.call(null);
      }

      return AnimatedSwitcher(
        duration: 300.milliseconds,
        child:
            controller.selectedStateId == Selection.none.id
                ? const SizedBox()
                : ObsListBuilder(
                  obs: controller.cities,
                  loader:
                      (context) =>
                          isRequired
                              ? const RequiredField(child: FieldLoadingWidget())
                              : const FieldLoadingWidget(),
                  errorBuilder:
                      (context, error) =>
                          isRequired
                              ? RequiredField(
                                child: FieldErrorWidget(
                                  error: error,
                                  onRefresh: controller.refreshCities,
                                ),
                              )
                              : FieldErrorWidget(
                                error: error,
                                onRefresh: controller.refreshCities,
                              ),
                  builder: (context, cities) {
                    return AppDropdownField(
                      hint: tr(LocaleKeys.city),
                      value: cities.firstWhereOrNull(
                        (element) => element.id == controller.selectedCityId,
                      ),
                      validator: isRequired ? Validator.notNull : null,
                      icon: Assets.icons.building,
                      supportNone: supportNone,
                      selections: cities,
                      onChanged: (value) {
                        onCityChanged?.call(value);
                      },
                    );
                  },
                ),
      );
    });
  }
}

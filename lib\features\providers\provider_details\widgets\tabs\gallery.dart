import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:tredo/core/assets/assets.gen.dart';
import 'package:tredo/core/style/repo.dart';
import 'package:tredo/core/style/style.dart';
import 'package:tredo/core/widgets/image.dart';
import 'package:tredo/features/usecases/pictures/index.dart';

class GalleryTab extends StatelessWidget {
  final List<String> images;

  GalleryTab({super.key, required this.images});

  final Rx<int> _currentAd = 0.obs;
  int get currentAd => _currentAd.value;
  set currentAd(int value) => _currentAd.value = value;

  @override
  Widget build(BuildContext context) {
    if (images.isEmpty) {
      return SliverList(
        delegate: SliverChildListDelegate([Assets.icons.noData.svg()]),
      );
    }

    return SliverList(
      delegate: SliverChildListDelegate([
        SizedBox(
          height: 360,
          child: Column(
            children: [
              Expanded(
                child: CarouselSlider(
                  options: CarouselOptions(
                    onPageChanged: (index, _) => currentAd = index,
                    autoPlay: true,
                    enlargeCenterPage: true,
                    aspectRatio: 16 / 9,
                  ),
                  items: List.generate(
                    images.length,
                    (index) => InkWell(
                      onTap:
                          () => MediaPreview.preview(
                            media: images,
                            initialIndex: index,
                          ),
                      child: AppImage(
                        path: images[index],
                        height: 300,
                        decoration: BoxDecoration(
                          color: context.appColorScheme.primaryColor,
                          borderRadius: BorderRadius.circular(5),
                          border: Border.all(
                            color: context.appColorScheme.primaryColor,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
              const Gap(12),
              SizedBox(
                height: 12,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: List.generate(
                    images.length,
                    (index) => Obx(
                      () => AnimatedContainer(
                        duration: 300.milliseconds,
                        margin: const EdgeInsets.symmetric(horizontal: 4),
                        height: 10,
                        width: currentAd == index ? 30 : 10,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(5),
                          color:
                              currentAd == index
                                  ? context.appColorScheme.secondaryColor
                                  : StyleRepo.purple,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
              const Gap(24),
            ],
          ),
        ),
      ]),
    );
  }
}

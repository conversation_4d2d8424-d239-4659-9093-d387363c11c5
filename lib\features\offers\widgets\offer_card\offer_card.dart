import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:tredo/core/models/offer/offer.dart';
import 'package:tredo/core/routes/navigation.dart';
import 'package:tredo/core/widgets/image.dart';
import 'package:tredo/features/providers/provider_details/models/nav.dart';
import 'style/style.dart';

class OfferCard extends StatelessWidget {
  final bool withProviderData;
  final Offer offer;
  const OfferCard({
    super.key,
    this.withProviderData = true,
    required this.offer,
  });

  static const double aspectRatio = .8;

  @override
  Widget build(BuildContext context) {
    final cardTheme = context.offerCardTheme;
    return InkWell(
      onTap:
          () => Nav.to(
            Pages.provider,
            arguments: ProviderDetailsPageNav(
              providerId: offer.provider.id,
              initialTab: 2,
            ),
          ),
      child: AspectRatio(
        aspectRatio: aspectRatio,
        child: Stack(
          children: [
            SizedBox.expand(
              child: AppImage(
                path: offer.image.medium,
                decoration: BoxDecoration(
                  boxShadow: cardTheme.shadow,
                  borderRadius: BorderRadius.vertical(
                    top: const Radius.circular(4),
                    bottom:
                        withProviderData
                            ? Radius.zero
                            : const Radius.circular(4),
                  ),
                ),
              ),
            ),
            if (!withProviderData)
              PositionedDirectional(
                bottom: 12,
                start: 12,
                child: _ProviderAvatar(offer.provider.cover?.small),
              ),
            if (withProviderData)
              Positioned(
                bottom: 0,
                right: 0,
                left: 0,
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: cardTheme.providerDataBackgroundColor,
                    borderRadius: const BorderRadius.vertical(
                      top: Radius.circular(16),
                    ),
                  ),
                  child: Row(
                    children: [
                      _ProviderAvatar(offer.provider.cover?.small),
                      const Gap(8),
                      Expanded(
                        child: Text(
                          offer.provider.shopName,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                          style: cardTheme.providerNameStyle,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}

class _ProviderAvatar extends StatelessWidget {
  final String? image;
  const _ProviderAvatar(this.image);

  @override
  Widget build(BuildContext context) {
    final cardTheme = context.offerCardTheme;
    return AppImage(
      path: image ?? "",
      height: 45,
      width: 45,
      decoration: BoxDecoration(
        color: cardTheme.providerAvatarBorderColor,
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: cardTheme.providerAvatarBorderColor),
      ),
    );
  }
}

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:tredo/core/localization/strings.dart';
import 'package:tredo/core/style/repo.dart';

import '../../animations/animated_appearance.dart';
import '../status.dart';

class AppToast extends StatelessWidget {
  final String? title;
  final String message;
  final ToastStatus status;

  const AppToast({
    super.key,
    this.title,
    required this.message,
    this.status = ToastStatus.success,
  });

  @override
  Widget build(BuildContext context) {
    Color activeColor = status.color;
    Color onActiveColor = StyleRepo.purple.shade50;

    return AnimatedAppearance(
      transitionBuilder:
          (child, animation) => ScaleTransition(
            scale: animation,
            alignment: Alignment.bottomCenter,
            child: child,
          ),
      child: Material(
        color: Colors.transparent,
        child: Container(
          width: double.infinity,
          padding: const EdgeInsetsDirectional.only(start: 5),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10),
            color: activeColor,
          ),
          child: Container(
            padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 12),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              color: onActiveColor.withValues(alpha: .2),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  spacing: 8,
                  children: [
                    Icon(status.icon, color: onActiveColor),
                    Expanded(
                      child: Text(
                        title ??
                            (status == ToastStatus.success
                                ? tr(LocaleKeys.success)
                                : status == ToastStatus.warning
                                ? tr(LocaleKeys.warning)
                                : tr(LocaleKeys.failure)),
                        style: TextStyle(
                          color: onActiveColor,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
                Text(message, style: TextStyle(color: onActiveColor)),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

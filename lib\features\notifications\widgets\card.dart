import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:tredo/core/style/utils/context.dart';

import '../models/notification.dart';

class NotificationCard extends StatelessWidget {
  final NotificationModel notification;
  const NotificationCard({super.key, required this.notification});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(notification.title, style: context.textTheme.titleMedium),
        // Text(
        //   "April 25,2025 / 12:00 PM",
        //   style: context.textTheme.labelMedium!.copyWith(color: StyleRepo.grey),
        // ),
        const Gap(12),
        Text(notification.body),
      ],
    );
  }
}

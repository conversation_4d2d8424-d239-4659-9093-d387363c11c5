import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:tredo/core/assets/assets.gen.dart';
import 'package:tredo/core/localization/strings.dart';
import 'package:tredo/core/models/selection.dart';
import 'package:tredo/core/services/state_management/widgets/obs_widget.dart';
import 'package:tredo/core/utils/validator.dart';
import 'package:tredo/core/widgets/general/fields/dropdown_filed.dart';
import 'package:tredo/core/widgets/general/fields/field_error.dart';
import 'package:tredo/core/widgets/general/fields/field_loading.dart';
import 'package:tredo/core/widgets/general/fields/required_filed.dart';

import '../controller.dart';

class SubcategoriesDropdown extends GetView<CategoriesSubcategoriesController> {
  final int? initialSubcategory;
  final void Function(Selection? value)? onSubcategoryChanged;
  final bool supportNone;
  final bool isRequired;

  const SubcategoriesDropdown(
    this.initialSubcategory,
    this.onSubcategoryChanged,
    this.supportNone,
    this.isRequired, {
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (controller.selectedSubcategoryId == Selection.none.id) {
        onSubcategoryChanged?.call(null);
      }

      return AnimatedSwitcher(
        duration: 300.milliseconds,
        child:
            controller.selectedCategoryId == Selection.none.id
                ? const SizedBox()
                : ObsListBuilder(
                  obs: controller.subcategories,
                  loader:
                      (context) =>
                          isRequired
                              ? const RequiredField(child: FieldLoadingWidget())
                              : const FieldLoadingWidget(),
                  errorBuilder:
                      (context, error) =>
                          isRequired
                              ? RequiredField(
                                child: FieldErrorWidget(
                                  error: error,
                                  onRefresh: controller.refreshSubcategories,
                                ),
                              )
                              : FieldErrorWidget(
                                error: error,
                                onRefresh: controller.refreshSubcategories,
                              ),
                  builder: (context, subcategories) {
                    return AppDropdownField(
                      hint: tr(LocaleKeys.subcategory),
                      value: subcategories.firstWhereOrNull(
                        (element) =>
                            element.id == controller.selectedSubcategoryId,
                      ),
                      validator: isRequired ? Validator.notNull : null,
                      icon: Assets.icons.building,
                      supportNone: supportNone,
                      selections: subcategories,
                      onChanged: (value) {
                        onSubcategoryChanged?.call(value);
                      },
                    );
                  },
                ),
      );
    });
  }
}

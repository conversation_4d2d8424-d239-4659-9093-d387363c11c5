import 'package:get/get.dart';
import 'package:tredo/core/models/selection.dart';
import 'package:tredo/core/services/rest_api/rest_api.dart';
import 'package:tredo/core/services/state_management/obs.dart';

class CategoriesSubcategoriesController extends GetxController {
  final int? initialCategory;
  final int? initialSubcategory;

  CategoriesSubcategoriesController({
    this.initialCategory,
    this.initialSubcategory,
  }) {
    _selectedCategoryId = (initialCategory ?? Selection.none.id).obs;
    selectedSubcategoryId = (initialSubcategory ?? Selection.none.id);
  }

  @override
  onInit() {
    fetchCategories();
    super.onInit();
  }

  //SECTION - Categories
  ObsList<Selection> categories = ObsList([]);

  fetchCategories() async {
    ResponseModel response = await APIService.instance.requestAPI(
      Request(endPoint: EndPoints.categories, fromJson: Selection.fromJson),
    );
    if (response.success) {
      categories.value = response.data;
      if (selectedCategoryId != Selection.none.id) {
        fetchSubcaetgories();
      }
    } else {
      categories.error = response.message;
    }
  }

  refreshCategories() async {
    categories.reset();
    selectedCategoryId = Selection.none.id;
    await fetchCategories();
  }

  late final Rx<int> _selectedCategoryId;
  int get selectedCategoryId => _selectedCategoryId.value;
  set selectedCategoryId(int value) {
    _selectedCategoryId.value = value;
    if (value != Selection.none.id) {
      refreshSubcategories();
    } else {
      selectedSubcategoryId = Selection.none.id;
      subcategories.reset();
    }
  }

  //!SECTION

  //SECTION - Subcategories
  ObsList<Selection> subcategories = ObsList([]);
  fetchSubcaetgories() async {
    ResponseModel response = await APIService.instance.requestAPI(
      Request(
        endPoint: EndPoints.subcategories,
        params: {"category_ids[0]": selectedCategoryId},
        fromJson: Selection.fromJson,
      ),
    );
    if (response.success) {
      subcategories.value = response.data;
    } else {
      subcategories.error = response.message;
    }
  }

  refreshSubcategories() async {
    subcategories.reset();
    selectedSubcategoryId = Selection.none.id;
    await fetchSubcaetgories();
  }

  late int selectedSubcategoryId;

  //!SECTION
}

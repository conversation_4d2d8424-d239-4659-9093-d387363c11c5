import 'dart:convert';
import 'dart:developer';
import 'dart:math' as math;

import 'package:firebase_messaging/firebase_messaging.dart';

import '../local_notifications/local_notification.dart';

@pragma("vm:entry-point")
class FirebaseMessagingService {
  static late FirebaseMessaging firebaseMessaging;

  static init() async {
    try {
      firebaseMessaging = FirebaseMessaging.instance;
    } catch (_) {
      return;
    }

    RemoteMessage? messageOpenedApp =
        await firebaseMessaging.getInitialMessage();
    log(messageOpenedApp.toString());

    try {
      NotificationSettings settings = await firebaseMessaging.requestPermission(
        alert: true,
        announcement: false,
        badge: true,
        carPlay: false,
        criticalAlert: false,
        provisional: false,
        sound: true,
      );
      log('User granted permission: ${settings.authorizationStatus}');
    } catch (_) {}

    firebaseMessaging.setForegroundNotificationPresentationOptions(
      alert: true,
      badge: true,
      sound: true,
    );

    initListeners();
    return false;
  }

  static void initListeners() async {
    FirebaseMessaging.onMessage.listen(getNotification);
  }

  @pragma("vm:entry-point")
  static getNotification(RemoteMessage message) async {
    log("${message.toMap()}", name: "FIREBASE");
    log("${message.data['id'].runtimeType}", name: "FIREBASE");

    Map<String, dynamic> data = message.data;

    LocalNotificationService.showNotification(
      id: math.Random().nextInt(5000),
      title: message.notification?.title ?? "No title sent",
      subTitle: message.notification?.body ?? "No body sent",
      payload: jsonEncode(data),
    );
  }

  static Future<String?> getToken() async {
    String? token;
    try {
      token = await firebaseMessaging.getToken();
    } catch (e) {
      log(e.toString());
    }
    log('FCM_TOKEN: $token', name: "FIREBASE");
    return token;
  }

  static Future<bool> deleteToken() async {
    try {
      await firebaseMessaging.deleteToken();
      return true;
    } catch (_) {
      return false;
    }
  }

  static Future<String?> regenerateToken() async {
    try {
      await firebaseMessaging.deleteToken();
    } catch (_) {
      return null;
    }
    return await getToken();
  }

  static Future<bool> subscribeToTopic(String topic) async {
    try {
      await firebaseMessaging.subscribeToTopic(topic);
      log("Subscribed to $topic", name: "FIREBASE");
    } catch (_) {
      return false;
    }
    return true;
  }

  static Future<bool> unsubscribeFromTopic(String topic) async {
    try {
      await firebaseMessaging.unsubscribeFromTopic(topic);
      log("Unubscribed from $topic", name: "FIREBASE");
    } catch (_) {
      return false;
    }
    return true;
  }
}

import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:tredo/core/widgets/general/pages/scaffold.dart';

import 'controller.dart';
import 'widgets/app_bar.dart';
import 'widgets/best_offers.dart';
import 'widgets/categories.dart';
import 'widgets/entities.dart';
import 'widgets/providers.dart';
import 'widgets/slider.dart';

class HomePage extends StatelessWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(HomePageController());

    return AppScaffold(
      appBar: const HomeAppBar(),
      body: RefreshIndicator(
        onRefresh: controller.refreshData,
        child: ListView(
          padding: const EdgeInsets.only(top: 24, bottom: 70),
          children: [
            CarouselAds(),
            const CategoriesList(),
            const Gap(24),
            const EntitiesList(),
            const Gap(24),
            const BestOffersList(),
            const Gap(24),
            const ProvidersList(),
            const Gap(24),
          ],
        ),
      ),
    );
  }
}

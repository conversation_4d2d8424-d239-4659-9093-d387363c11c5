import 'package:dio/dio.dart';
import 'package:get/get.dart';
import 'package:tredo/core/services/pagination/controller.dart';
import 'package:tredo/core/services/rest_api/rest_api.dart';

import '../../usecases/filters/index.dart';
import '../../usecases/filters/models/filter_result.dart';
import '../../usecases/filters/models/filters_params.dart';

class ProvidersPageController extends GetxController {
  FiltersResult filters = FiltersResult();

  late PaginationController pagerController;

  onFilter() async {
    final result = await FiltersBottomSheet.showBottomSheet(
      params: FiltersParams(initialFilters: filters.toInitialFilters()),
    );
    if (result == null) return null;

    filters = result;
    pagerController.refreshData();
  }

  String search = '';
  onSearch(String search) {
    this.search = search;
    pagerController.refreshData();
  }

  Future<ResponseModel> fetchProviders(int page, CancelToken cancel) async {
    return await APIService.instance.requestAPI(
      Request(
        endPoint: EndPoints.providers,
        params: {
          'page': page,
          if (search.trim().isNotEmpty) "name": search,
          if (filters.categoryId != null) 'category_id': filters.categoryId,
          if (filters.subcategoryId != null)
            'sub_category_id': filters.subcategoryId,
          if (filters.stateId != null) 'state_id': filters.stateId,
          if (filters.cityId != null) 'city_id': filters.cityId,
        },
        cancelToken: cancel,
      ),
    );
  }
}

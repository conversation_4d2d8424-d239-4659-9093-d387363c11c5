import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:navigation_history_observer/navigation_history_observer.dart';
import 'package:tredo/core/config/app_builder.dart';
import 'package:tredo/core/config/role.dart';
import 'package:tredo/core/routes/navigation.dart';
import 'package:tredo/core/services/firebase_messaging/firebase_messaging.dart';
import 'package:tredo/core/services/rest_api/rest_api.dart';
import 'package:tredo/core/widgets/general/toast/toast.dart';
import 'package:tredo/core/widgets/general/loading/loading.dart';
import 'package:tredo/features/auth/verification/models/nav.dart';

class RegisterPageController extends GetxController {
  AppBuilder appBuilder = Get.find();

  final formKey = GlobalKey<FormState>();

  late TextEditingController firstName,
      lastName,
      phone,
      password,
      confirmPassword;

  int? cityId;

  @override
  void onInit() {
    firstName = TextEditingController();
    lastName = TextEditingController();
    phone = TextEditingController();
    password = TextEditingController();
    confirmPassword = TextEditingController();
    super.onInit();
  }

  @override
  void onClose() {
    firstName.dispose();
    lastName.dispose();
    phone.dispose();
    password.dispose();
    confirmPassword.dispose();
    super.onClose();
  }

  //SECTION - Terms and condition
  final Rx<bool> _isTermsAccepted = false.obs;
  bool get isTermsAccepted => _isTermsAccepted.value;
  set isTermsAccepted(bool value) => _isTermsAccepted.value = value;

  openTerms() {
    Nav.to(Pages.privacy_policy);
  }
  //!SECTION

  Future<void> register() async {
    if (!formKey.currentState!.validate()) return;

    Loading.show();

    ResponseModel response = await APIService.instance.requestAPI(
      Request(
        endPoint: EndPoints.register,
        method: RequestMethod.Post,
        body: {
          "first_name": firstName.text,
          "last_name": lastName.text,
          "phone": phone.text,
          "password": password.text,
          "password_confirmation": confirmPassword.text,
          "city_id": cityId,
          "fcm_token": await FirebaseMessagingService.getToken(),
        },
      ),
    );

    Loading.dispose();

    if (response.success) {
      bool? result = await Nav.to(
        Pages.verification,
        arguments: VerificationPageNav(phone: phone.text),
      );
      if (result ?? false) {
        appBuilder.setRole(const User());
        await appBuilder.role.initialize();
        Nav.offUntil(appBuilder.role.landing, (_) => false);
      }
    } else {
      Toast.show(message: response.message);
    }
  }

  void login() {
    if (NavigationHistoryObserver().history
        .asList()
        .map((route) => route.settings.name)
        .contains(Pages.login.value)) {
      Get.until((route) => route.settings.name == Pages.login.value);
    } else {
      Nav.offAll(Pages.login);
    }
  }
}

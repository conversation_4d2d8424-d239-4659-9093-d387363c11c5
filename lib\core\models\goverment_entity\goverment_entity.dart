import 'dart:convert';

import '../social_link.dart';

class GovermentEntity {
  int id;
  String name;
  String svgImage;
  List<SocialLink> socialLinks;

  GovermentEntity({
    required this.id,
    required this.name,
    required this.svgImage,
    required this.socialLinks,
  });

  GovermentEntity copyWith({
    int? id,
    String? name,
    String? svgImage,
    List<SocialLink>? socialLinks,
  }) => GovermentEntity(
    id: id ?? this.id,
    name: name ?? this.name,
    svgImage: svgImage ?? this.svgImage,
    socialLinks: socialLinks ?? this.socialLinks,
  );

  factory GovermentEntity.fromRawJson(String str) =>
      GovermentEntity.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory GovermentEntity.fromJson(Map<String, dynamic> json) =>
      GovermentEntity(
        id: json["id"],
        name: json["name"],
        svgImage: json["svg_image"],
        socialLinks: List<SocialLink>.from(
          json["socialLinks"].map((x) => SocialLink.fromJson(x)),
        ),
      );

  Map<String, dynamic> toJson() => {
    "id": id,
    "name": name,
    "svg_image": svgImage,
    "socialLinks": List<dynamic>.from(socialLinks.map((x) => x.toJson())),
  };
}

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tredo/core/localization/strings.dart';
import 'package:tredo/core/services/rest_api/rest_api.dart';
import 'package:tredo/core/widgets/general/toast/toast.dart';

class ContactPageController extends GetxController {
  final formKey = GlobalKey<FormState>();

  late TextEditingController firstName, lastName, phone, description;

  @override
  void onInit() {
    firstName = TextEditingController();
    lastName = TextEditingController();
    phone = TextEditingController();
    description = TextEditingController();
    super.onInit();
  }

  @override
  void onClose() {
    firstName.dispose();
    lastName.dispose();
    phone.dispose();
    description.dispose();
    super.onClose();
  }

  confirm() async {
    if (!formKey.currentState!.validate()) return;

    ResponseModel response = await APIService.instance.requestAPI(
      Request(
        endPoint: EndPoints.contact_us,
        method: RequestMethod.Post,
        body: {
          "first_name": firstName.text,
          "last_name": lastName.text,
          "phone": phone.text,
          "message": description.text,
        },
      ),
    );

    if (response.success) {
      Get.back();
      Toast.show(
        message: tr(LocaleKeys.your_message_sent_successfully),
        status: ToastStatus.success,
      );
    } else {
      Toast.show(message: response.message);
    }
  }
}

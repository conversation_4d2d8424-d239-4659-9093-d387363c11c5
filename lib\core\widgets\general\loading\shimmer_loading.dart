import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';
import 'package:tredo/core/style/repo.dart';

class ShimmerWidget extends StatelessWidget {
  final Widget child;
  final Color? baseColor;
  final Color? highlightColor;
  final BorderRadiusGeometry? borderRadius;

  const ShimmerWidget({
    super.key,
    this.baseColor,
    this.highlightColor,
    this.child = const ColoredBox(color: StyleRepo.white),
    this.borderRadius,
  });

  @override
  Widget build(BuildContext context) {
    Widget shimmer = Shimmer.fromColors(
      baseColor: baseColor ?? StyleRepo.purple.shade600,
      highlightColor: StyleRepo.lightGrey,
      child: child,
    );

    if (borderRadius != null) {
      shimmer = ClipRRect(borderRadius: borderRadius!, child: shimmer);
    }

    return shimmer;
  }
}

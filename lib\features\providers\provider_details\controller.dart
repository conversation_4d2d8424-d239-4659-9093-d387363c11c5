import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tredo/core/services/rest_api/rest_api.dart';
import 'package:tredo/core/services/state_management/obs.dart';

import 'models/nav.dart';
import 'models/provider.dart';

class ProviderDetailsPageController extends GetxController {
  final ProviderDetailsPageNav nav;
  ProviderDetailsPageController(this.nav) {
    _currentTabIndex = (nav.initialTab ?? 0).obs;
  }

  ScrollController scrollController = ScrollController();

  @override
  void onInit() {
    fetchProviderDetails();
    super.onInit();
  }

  late final Rx<int> _currentTabIndex;
  int get currentTabIndex => _currentTabIndex.value;
  set currentTabIndex(int value) => _currentTabIndex.value = value;

  ObsVar<ProviderDetails> provider = ObsVar(null);

  fetchProviderDetails() async {
    ResponseModel response = await APIService.instance.requestAPI(
      Request(
        endPoint: EndPoints.provider_details(nav.providerId),
        fromJson: ProviderDetails.fromJson,
      ),
    );
    if (response.success) {
      provider.value = response.data;
    } else {
      provider.error = response.message;
    }
  }

  Future refreshData() async {
    provider.reset();
    await fetchProviderDetails();
  }

  Future<ResponseModel> fetchOffers(int page, CancelToken cancel) async {
    return await APIService.instance.requestAPI(
      Request(
        endPoint: EndPoints.offers,
        params: {"page": page, "provider_id": nav.providerId},
        cancelToken: cancel,
      ),
    );
  }
}

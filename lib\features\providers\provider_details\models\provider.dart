import 'package:tredo/core/models/category/category.dart';
import 'package:tredo/core/models/image.dart';
import 'package:tredo/core/models/provider/main_provider.dart';
import 'package:tredo/core/models/selection.dart';
import 'package:tredo/core/models/social_link.dart';

class ProviderDetails extends MainProvider {
  final Selection state, city;
  final Category category, subcategory;
  final String description;
  final List<ImageModel> gallery;
  List<SocialLink> links;

  ProviderDetails({
    required super.id,
    required super.shopName,
    required super.views,
    required super.cover,
    required super.tags,
    required this.state,
    required this.city,
    required this.category,
    required this.subcategory,
    required this.description,
    required this.gallery,
    required this.links,
  });

  factory ProviderDetails.fromJson(Map<String, dynamic> json) {
    bool emptyMedia = json['media'] == null || json['media'] is List;
    return ProviderDetails(
      id: json["id"],
      shopName: json["shop_name"],
      views: json["views"],
      cover: emptyMedia ? null : ImageModel.fromJson(json["media"]['image'][0]),
      tags:
          json["tags"] != null
              ? List<String>.from(json["tags"].map((x) => x['name']))
              : [],
      state: Selection.fromJson(json['city']['state']),
      city: Selection.fromJson(json['city']),
      category: Category.fromJson(json['subCategory']['category']),
      subcategory: Category.fromJson(json['subCategory']),
      description: json['description'],
      gallery:
          emptyMedia
              ? []
              : List<ImageModel>.from(
                (json['media']['gallery'] ?? []).map(
                  (x) => ImageModel.fromJson(x),
                ),
              ),
      links: List<SocialLink>.from(
        (json['socialLinks'] ?? []).map((x) => SocialLink.fromJson(x)),
      ),
    );
  }
}

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:tredo/core/localization/strings.dart';
import 'package:tredo/core/widgets/general/fields/prefix.dart';
import 'package:tredo/core/widgets/general/svg_icon.dart';

import '../../../utils/debouncer.dart';

class SearchField extends StatelessWidget {
  final String? initialValue;
  final void Function(String value)? onChanged;
  final VoidCallback? onFilter;

  SearchField({super.key, this.onChanged, this.initialValue, this.onFilter}) {
    if (onChanged != null) {
      debouncer = Debouncer(
        execute: onChanged!,
        time: const Duration(milliseconds: 500),
      );
    }
  }

  late final Debouncer<String> debouncer;

  @override
  Widget build(BuildContext context) {
    return TextField(
      onChanged: (value) => debouncer.add(value),
      decoration: InputDecoration(
        hintText: tr(LocaleKeys.search),
        prefixIcon: FieldPrefix(child: SvgIcon(Assets.icons.search)),
        suffixIcon:
            onFilter != null
                ? FieldPrefix(
                  padding: const EdgeInsets.symmetric(horizontal: 4),
                  child: Material(
                    color: Colors.transparent,
                    child: InkWell(
                      onTap: onFilter,
                      borderRadius: BorderRadius.circular(50),
                      child: Padding(
                        padding: const EdgeInsets.all(12.0),
                        child: SvgIcon(Assets.icons.filter),
                      ),
                    ),
                  ),
                )
                : null,
      ),
    );
  }
}

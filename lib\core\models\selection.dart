import 'dart:convert';

import 'package:easy_localization/easy_localization.dart';
import 'package:tredo/core/localization/strings.dart';

class Selection {
  int id;
  String name;

  Selection({required this.id, required this.name});

  static Selection none = Selection(id: 0, name: tr(LocaleKeys.none));

  factory Selection.fromRawJson(String str) =>
      Selection.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory Selection.fromJson(Map<String, dynamic> json) =>
      Selection(id: json["id"], name: json["name"]);

  Map<String, dynamic> toJson() => {"id": id, "name": name};
}

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:tredo/core/localization/strings.dart';
import 'package:tredo/core/services/state_management/widgets/obs_widget.dart';
import 'package:tredo/core/style/style.dart';
import 'package:tredo/core/widgets/general/error/error_card.dart';
import 'package:tredo/core/widgets/general/loading/shimmer_loading.dart';
import 'package:tredo/features/providers/widgets/provider_card/provider_card.dart';

import '../../main/controller.dart';
import '../controller.dart';

class ProvidersList extends GetView<HomePageController> {
  const ProvidersList({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            children: [
              Expanded(
                child: Text(
                  tr(LocaleKeys.service_providers),
                  style: context.textTheme.titleLarge,
                ),
              ),
              TextButton(
                onPressed:
                    () => Get.find<MainPageController>().navToProviders(),
                child: Text(
                  tr(LocaleKeys.show_all),
                  style: context.textTheme.bodyMedium!.copyWith(
                    color: context.appColorScheme.secondaryColor.shade700,
                  ),
                ),
              ),
            ],
          ),
        ),
        const Gap(16),
        ObsListBuilder(
          obs: controller.providers,
          loader:
              (context) => SizedBox(
                height: 270,
                child: ListView.separated(
                  scrollDirection: Axis.horizontal,
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  itemCount: 10,
                  separatorBuilder: (_, __) => const Gap(12),
                  itemBuilder:
                      (context, index) => SizedBox(
                        width: MediaQuery.sizeOf(context).width * .4,
                        child: ShimmerWidget(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                ),
              ),
          errorBuilder:
              (context, error) => Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: SizedBox(
                  height: 270,
                  child: ErrorCard(
                    error: error,
                    onRefresh: controller.refreshProviders,
                    borderRadius: BorderRadius.circular(15),
                  ),
                ),
              ),
          builder: (context, providers) {
            return SizedBox(
              height: 270,
              child: ListView.separated(
                scrollDirection: Axis.horizontal,
                padding: const EdgeInsets.symmetric(horizontal: 16),
                itemCount: providers.length,
                separatorBuilder: (_, __) => const Gap(12),
                itemBuilder:
                    (context, index) =>
                        ProviderCard(provider: providers[index]),
              ),
            );
          },
        ),
      ],
    );
  }
}

import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:tredo/core/services/state_management/widgets/obs_widget.dart';
import 'package:tredo/core/style/repo.dart';
import 'package:tredo/core/style/style.dart';
import 'package:tredo/core/widgets/general/error/error_card.dart';
import 'package:tredo/core/widgets/general/loading/shimmer_loading.dart';
import 'package:tredo/core/widgets/image.dart';

import '../controller.dart';

class CarouselAds extends GetView<HomePageController> {
  final double aspectRatio;

  CarouselAds({super.key, this.aspectRatio = 16 / 9});

  final Rx<int> _currentAd = 0.obs;
  int get currentAd => _currentAd.value;
  set currentAd(int value) => _currentAd.value = value;

  @override
  Widget build(BuildContext context) {
    return ObsListBuilder(
      obs: controller.ads,
      loader:
          (context) => Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: AspectRatio(
              aspectRatio: aspectRatio,
              child: ShimmerWidget(borderRadius: BorderRadius.circular(15)),
            ),
          ),
      errorBuilder:
          (context, error) => Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: AspectRatio(
              aspectRatio: aspectRatio,
              child: ErrorCard(
                error: error,
                onRefresh: controller.refreshAds,
                borderRadius: BorderRadius.circular(15),
              ),
            ),
          ),
      builder: (context, ads) {
        if (ads.isEmpty) {
          return const SizedBox();
        }
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CarouselSlider(
              options: CarouselOptions(
                onPageChanged: (index, _) => currentAd = index,
                autoPlay: true,
                enlargeCenterPage: true,
                aspectRatio: aspectRatio,
              ),
              items: List.generate(
                ads.length,
                (index) => InkWell(
                  onTap: () => controller.clickAd(ads[index]),
                  child: AspectRatio(
                    aspectRatio: aspectRatio,
                    child: AppImage(
                      path: ads[index].image.large,
                      width: double.infinity,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(15),
                      ),
                    ),
                  ),
                ),
              ),
            ),
            const Gap(12),
            SizedBox(
              height: 12,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: List.generate(
                  ads.length,
                  (index) => Obx(
                    () => AnimatedContainer(
                      duration: 300.milliseconds,
                      margin: const EdgeInsets.symmetric(horizontal: 4),
                      height: 10,
                      width: currentAd == index ? 30 : 10,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(5),
                        color:
                            currentAd == index
                                ? context.appColorScheme.secondaryColor
                                : StyleRepo.purple,
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}

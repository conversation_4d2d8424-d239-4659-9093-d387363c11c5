import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:get/get.dart';
import 'package:tredo/core/localization/strings.dart';
import 'package:tredo/core/services/state_management/widgets/obs_widget.dart';
import 'package:tredo/core/widgets/general/error/error_widget.dart';
import 'package:tredo/core/widgets/general/pages/scaffold.dart';

import 'controller.dart';

class PrivacyPolicyPage extends GetView<PrivacyPolicyPageController> {
  const PrivacyPolicyPage({super.key});

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      appBar: AppBar(title: Text(tr(LocaleKeys.privacy_policy))),
      body: ObsVariableBuilder(
        obs: controller.privacy,
        onRefresh: controller.refreshPrivacy,
        builder: (context, privacy) {
          if (privacy.isEmpty) {
            return AppErrorWidget(error: tr(LocaleKeys.no_data));
          }
          return SingleChildScrollView(child: Html(data: privacy));
        },
      ),
    );
  }
}

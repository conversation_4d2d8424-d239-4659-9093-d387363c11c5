import 'package:flutter/material.dart';
import 'package:tredo/core/style/repo.dart';
import 'package:package_info_plus/package_info_plus.dart';

class VersionWidget extends StatelessWidget {
  const VersionWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
      future: PackageInfo.fromPlatform(),
      builder: (context, snap) {
        if (snap.hasData && snap.data != null) {
          return Center(
            child: Text(
              "V. ${snap.data!.version} +${snap.data!.buildNumber}",
              textAlign: TextAlign.center,
              style: const TextStyle(color: StyleRepo.grey, fontSize: 20),
            ),
          );
        }
        return const SizedBox();
      },
    );
  }
}

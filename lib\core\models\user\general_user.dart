import 'dart:convert';

import '../selection.dart';

class GeneralUser {
  int id;
  String firstName;
  String lastName;
  String phone;
  Selection city;
  Selection state;
  String image;

  GeneralUser({
    required this.id,
    required this.firstName,
    required this.lastName,
    required this.phone,
    required this.city,
    required this.state,
    required this.image,
  });

  GeneralUser copyWith({
    int? id,
    String? firstName,
    String? lastName,
    String? phone,
    Selection? city,
    Selection? state,
    String? image,
  }) => GeneralUser(
    id: id ?? this.id,
    firstName: firstName ?? this.firstName,
    lastName: lastName ?? this.lastName,
    phone: phone ?? this.phone,
    city: city ?? this.city,
    state: state ?? this.state,
    image: image ?? this.image,
  );

  factory GeneralUser.fromRawJson(String str) =>
      GeneralUser.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory GeneralUser.fromJson(Map<String, dynamic> json) => GeneralUser(
    id: json["id"],
    firstName: json["first_name"],
    lastName: json["last_name"],
    phone: json["phone"],
    city:
        json["city"] is String
            ? Selection.fromRawJson(json["city"])
            : Selection.fromJson(json["city"]),
    state:
        json["city"]["state"] is String
            ? Selection.fromRawJson(json["city"]["state"])
            : Selection.fromJson(json["city"]["state"]),
    image: json["image"],
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "first_name": firstName,
    "last_name": lastName,
    "phone": phone,
    "city": {...city.toJson(), "state": state.toJson()},
    "image": image,
  };
}

import 'package:get/get.dart';
import 'package:tredo/core/models/social_link.dart';
import 'package:tredo/core/services/rest_api/rest_api.dart';
import 'package:tredo/core/services/state_management/obs.dart';

class AboutPageController extends GetxController {
  @override
  void onInit() {
    fetchLinks();
    fetchAbout();
    super.onInit();
  }

  //SECTION - Links
  ObsList<SocialLink> links = ObsList([]);

  fetchLinks() async {
    ResponseModel response = await APIService.instance.requestAPI(
      Request(endPoint: EndPoints.contacts, fromJson: SocialLink.fromJson),
    );
    if (response.success) {
      links.value = response.data;
    } else {
      links.error = response.message;
    }
  }

  refreshLinks() {
    links.reset();
    fetchLinks();
  }
  //!SECTION

  //SECTION - about data
  ObsVar<String> about = ObsVar(null);

  fetchAbout() async {
    ResponseModel response = await APIService.instance.requestAPI(
      Request(endPoint: EndPoints.about),
    );
    if (response.success) {
      if (response.data is! List || response.data.isEmpty) {
        about.value = "";
      } else {
        about.value = response.data.first['content'];
      }
    } else {
      about.error = response.message;
    }
  }

  refreshAbout() {
    about.reset();
    fetchAbout();
  }

  //!SECTION
}

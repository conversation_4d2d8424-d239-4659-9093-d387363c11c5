import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:tredo/core/constants/controllers_tags.dart';
import 'package:tredo/core/models/provider/main_provider.dart';
import 'package:tredo/core/services/pagination/options/list_view.dart';
import 'package:tredo/core/widgets/general/fields/search_field.dart';
import 'package:tredo/core/widgets/general/loading/shimmer_loading.dart';
import 'package:tredo/core/widgets/general/pages/scaffold.dart';

import '../widgets/provider_tile/provider_tile.dart';
import 'controller.dart';
import 'widgets/app_bar.dart';
import 'widgets/subcaegories.dart';

class CategoryProvidersPage extends GetView<CategoryProvidersPageController> {
  const CategoryProvidersPage({super.key});

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      resizeToAvoidBottomInset: false,
      appBar: const CategoryProvidersAppBar(),
      body: Column(
        children: [
          const Gap(16),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: SearchField(
              onFilter: controller.onFilter,
              onChanged: controller.onSearch,
            ),
          ),
          const Gap(8),
          const SubcategoriesList(),
          const Gap(8),
          const Divider(height: 0),
          Expanded(
            child: ListViewPagination.separated(
              tag: ControllersTags.category_providers_pager,
              fetchApi: controller.fetchProviders,
              fromJson: MainProvider.fromJson,
              onControllerInit:
                  (paginationController) =>
                      controller.pagerController = paginationController,
              padding: const EdgeInsets.only(
                top: 12,
                bottom: 70,
                left: 16,
                right: 16,
              ),
              separatorBuilder: (_, __) => const Gap(12),
              loading: SizedBox(
                height: 115,
                child: ShimmerWidget(borderRadius: BorderRadius.circular(8)),
              ),
              initialLoading: ListView.separated(
                itemCount: 10,
                separatorBuilder: (_, __) => const Gap(12),
                padding: const EdgeInsets.only(
                  top: 12,
                  bottom: 70,
                  left: 16,
                  right: 16,
                ),
                itemBuilder:
                    (context, index) => SizedBox(
                      height: 115,
                      child: ShimmerWidget(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
              ),
              itemBuilder:
                  (context, index, provider) =>
                      ProviderTile(provider: provider),
            ),
          ),
        ],
      ),
    );
  }
}

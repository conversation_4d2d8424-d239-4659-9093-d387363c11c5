import 'package:flutter/widgets.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:tredo/core/models/category/category.dart';
import 'package:tredo/core/services/state_management/widgets/obs_widget.dart';
import 'package:tredo/core/widgets/general/error/error_card.dart';
import 'package:tredo/features/categories/widgets/category_card/category_card.dart';

import '../controller.dart';

class SubcategoriesList extends GetView<CategoryProvidersPageController> {
  const SubcategoriesList({super.key});

  @override
  Widget build(BuildContext context) {
    return ObsListBuilder(
      obs: controller.subcategories,
      loader:
          (context) => SizedBox(
            height: 100,
            child: ListView.separated(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: 10,
              separatorBuilder: (_, __) => const Gap(12),
              itemBuilder: (context, index) => const CategoryCardLoading(),
            ),
          ),
      errorBuilder:
          (context, error) => Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: SizedBox(
              height: 70,
              child: ErrorCard(
                error: error,
                onRefresh: controller.refreshSubcategories,
                borderRadius: BorderRadius.circular(10),
              ),
            ),
          ),
      builder: (context, subcategories) {
        return SizedBox(
          height: 70,
          child: ListView.separated(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: subcategories.length + 1,
            separatorBuilder: (_, __) => const Gap(12),
            itemBuilder: (context, index) {
              Category category;
              if (index == 0) {
                category = Category.all;
              } else {
                category = subcategories[index - 1];
              }
              return Obx(
                () => CategoryCard(
                  category: category,
                  onTap: () => controller.selectedSubcategoryId = category.id,
                  selected: controller.selectedSubcategoryId == category.id,
                ),
              );
            },
          ),
        );
      },
    );
  }
}

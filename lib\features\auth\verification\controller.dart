import 'package:get/get.dart';
import 'package:tredo/core/config/app_builder.dart';
import 'package:tredo/core/config/role.dart';
import 'package:tredo/core/models/user/general_user.dart';
import 'package:tredo/core/services/rest_api/rest_api.dart';
import 'package:tredo/core/utils/timer.dart';
import 'package:tredo/core/widgets/general/toast/toast.dart';
import 'package:tredo/core/widgets/general/loading/loading.dart';

import 'models/nav.dart';

const int _kResendTime = 30;

class VerificationPageController extends GetxController with ResendCodeTimer {
  final VerificationPageNav nav;
  VerificationPageController(this.nav);

  AppBuilder appBuilder = Get.find();

  @override
  void onInit() {
    startTimer(_kResendTime);
    super.onInit();
  }

  final Rx<String> _code = "".obs;
  String get code => _code.value;
  set code(String value) => _code.value = value;

  final Rx<String> _error = "".obs;
  String get error => _error.value;
  set error(String value) => _error.value = value;

  void verify() async {
    error = "";

    Loading.show();

    ResponseModel response = await APIService.instance.requestAPI(
      Request(
        endPoint: EndPoints.verify_code,
        method: RequestMethod.Post,
        body: {
          "phone": nav.phone,
          "code": code,
          "for_verifiy_account": nav.isForVerifyAccount ? 1 : 0,
        },
      ),
    );

    Loading.dispose();

    if (response.success) {
      if (nav.isForVerifyAccount) {
        appBuilder.setUserData(
          role: const User(),
          token: response.data['token'],
          user: GeneralUser.fromJson(response.data['user']),
        );
        Get.back(result: true);
        return;
      }
      Get.back(result: code);
    } else {
      error = response.message;
    }
  }

  void resend() async {
    Loading.show();

    ResponseModel response = await APIService.instance.requestAPI(
      Request(
        endPoint: EndPoints.resend_code,
        method: RequestMethod.Post,
        body: {"phone": nav.phone},
      ),
    );

    Loading.dispose();

    if (response.success) {
      startTimer(_kResendTime);
    } else {
      Toast.show(message: response.message);
    }
  }
}

import 'filter_result.dart';

class FiltersParams {
  final FiltersActivations activations;
  final InitialFilters initialFilters;

  FiltersParams({
    this.activations = const FiltersActivations(),
    this.initialFilters = const InitialFilters(),
  });
}

class FiltersActivations {
  final bool categories, place;

  const FiltersActivations({this.categories = true, this.place = true});
}

class InitialFilters {
  final int? categoryId, subcategoryId, stateId, cityId;

  const InitialFilters({
    this.categoryId,
    this.subcategoryId,
    this.stateId,
    this.cityId,
  });

  FiltersResult toFiltersResult() => FiltersResult(
    categoryId: categoryId,
    subcategoryId: subcategoryId,
    stateId: stateId,
    cityId: cityId,
  );
}

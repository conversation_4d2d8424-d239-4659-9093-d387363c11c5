import 'package:get/get.dart';
import 'package:tredo/core/services/rest_api/rest_api.dart';
import 'package:tredo/core/services/state_management/obs.dart';

class PrivacyPolicyPageController extends GetxController {
  ObsVar<String> privacy = ObsVar(null);

  @override
  onInit() {
    fetchPrivacy();
    super.onInit();
  }

  fetchPrivacy() async {
    ResponseModel response = await APIService.instance.requestAPI(
      Request(endPoint: EndPoints.privacy_policy),
    );
    if (response.success) {
      if (response.data is! List || response.data.isEmpty) {
        privacy.value = "";
      } else {
        privacy.value = response.data.first['content'];
      }
    } else {
      privacy.error = response.message;
    }
  }

  Future refreshPrivacy() async {
    privacy.reset();
    await fetchPrivacy();
  }
}

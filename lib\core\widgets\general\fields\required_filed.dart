import 'package:flutter/widgets.dart';
import 'package:gap/gap.dart';
import 'package:tredo/core/style/style.dart';
import 'package:tredo/core/style/utils/context.dart';

import '../../../utils/validator.dart';

class RequiredField extends StatelessWidget {
  final Widget child;
  const RequiredField({super.key, required this.child});

  @override
  Widget build(BuildContext context) {
    return FormField<String>(
      initialValue: null,
      validator: Validator.notNull,
      builder:
          (state) => Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              child,

              if (state.hasError) const Gap(8),
              if (state.hasError)
                Text(
                  state.errorText!,
                  style: context.textTheme.labelMedium!.copyWith(
                    color: context.appColorScheme.errorColor,
                  ),
                ),
            ],
          ),
    );
  }
}
